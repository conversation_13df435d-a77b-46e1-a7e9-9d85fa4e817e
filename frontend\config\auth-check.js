/**
 * Config Page Authentication Check
 * Updated to use universal auth check without redirects
 */

console.log('=== CONFIG AUTH CHECK SCRIPT (Updated) ===');

// Import universal auth check
let authChecker;

// Initialize authentication check
async function initConfigAuth() {
    try {
        console.log('🚀 Config: Initializing authentication system');
        
        // Wait for dependencies
        await waitForDependencies();
        
        // Create auth checker for Config module
        authChecker = new UniversalAuthCheck('Config');
        
        // Perform authentication check
        const authSuccess = await authChecker.performCheck();
        
        if (authSuccess) {
            console.log('✅ Config: Authentication successful, enabling functionality');
            enableConfigFunctionality();
        } else {
            console.log('❌ Config: Authentication failed, showing login options');
            // Note: No redirect to Dashboard - inline login will be shown
        }
        
        return authSuccess;
    } catch (error) {
        console.error('❌ Config: Authentication initialization failed:', error);
        return false;
    }
}

// Wait for required dependencies
async function waitForDependencies() {
    const maxWait = 10000; // 10 seconds
    const checkInterval = 100; // 100ms
    let waited = 0;

    while (waited < maxWait) {
        console.log(`🔍 Config: Checking dependencies... sessionManager=${!!window.sessionManager}, UniversalAuthCheck=${!!window.UniversalAuthCheck}, waited=${waited}ms`);

        if (window.sessionManager && window.UniversalAuthCheck) {
            console.log('✅ Config: Dependencies loaded');
            return true;
        }

        await new Promise(resolve => setTimeout(resolve, checkInterval));
        waited += checkInterval;
    }

    console.error('❌ Config: Dependencies not loaded within timeout');
    console.log('Available globals:', Object.keys(window).filter(k => k.includes('session') || k.includes('Auth')));
    throw new Error('Dependencies not loaded within timeout');
}

// Enable Config page functionality
function enableConfigFunctionality() {
    // Show main content
    const mainContent = document.querySelector('.main-content, #main-content, .config-content');
    if (mainContent) {
        mainContent.style.display = 'block';
    }
    
    // Enable form elements
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.style.pointerEvents = 'auto';
        form.style.opacity = '1';
    });
    
    // Enable buttons
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {
        button.disabled = false;
    });
    
    console.log('✅ Config: Functionality enabled');
}

// Listen for authentication events
window.addEventListener('auth:success', (event) => {
    if (event.detail.module === 'Config') {
        console.log('✅ Config: Auth success event received');
        enableConfigFunctionality();
    }
});

window.addEventListener('session:updated', (event) => {
    console.log('🔄 Config: Session updated, re-checking auth');
    if (authChecker) {
        authChecker.performCheck();
    }
});

window.addEventListener('session:cleared', (event) => {
    console.log('🧹 Config: Session cleared, disabling functionality');
    // Don't redirect, just show login
    if (authChecker) {
        authChecker.handleAuthFailure();
    }
});

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initConfigAuth);
} else {
    initConfigAuth();
}

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initConfigAuth,
        enableConfigFunctionality
    };
}
