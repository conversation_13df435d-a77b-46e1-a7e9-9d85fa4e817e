async function rerunFailedTests(testId) {
    try {
        console.log(`Rerunning failed tests for test ${testId}...`);

        const credentials = window.reportsCredentials.get();
        if (!credentials) {
            window.reportsCredentials.onAvailable(() => rerunFailedTests(testId));
            return;
        }

        // Check if we have access to the dashboard API integration
        if (window.parent && window.parent.dashboardApiIntegration) {
            // Use the parent window's dashboard API integration
            window.parent.dashboardApiIntegration.rerunFailedTests(testId)
                .then(newTestId => {
                    if (newTestId) {
                        // Redirect to the dashboard to view the new test run
                        window.location.href = `../dashboard/index.html?testId=${newTestId}`;
                    }
                })
                .catch(error => {
                    console.error('Error rerunning failed tests:', error);
                    alert(`Failed to rerun tests: ${error.message}`);
                });
        } else {
            // If we don't have access to the dashboard API integration,
            // use the API directly

            // Construct the URL for the rerun failed endpoint
            const url = new URL('/api/rerun-failed', window.location.origin);
            url.searchParams.append('uid', credentials.uid);
            url.searchParams.append('password', credentials.password);
            url.searchParams.append('tsn_id', testId);

            // Call the API to rerun failed tests
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ tsn_id: testId })
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Failed to rerun tests: ${response.status} - ${errorText}`);
            }

            const data = await response.json();
            console.log('Rerun failed tests response:', data);

            // Check for success in the refactored API response format
            if (!data.success) throw new Error(data.message || 'API error');

            // Extract the new test ID from the response
            const newTestId = data.data?.tsn_id;

            if (newTestId) {
                // Redirect to the dashboard to view the new test run
                window.location.href = `../dashboard/index.html?testId=${newTestId}`;
            } else {
                // If no new test ID is returned, redirect to the dashboard with a parameter to rerun the tests
                window.location.href = `../dashboard/index.html?rerunFailed=${testId}`;
            }
        }
    } catch (error) {
        console.error('Error rerunning failed tests:', error);
        alert(`Failed to rerun tests: ${error.message}`);
    }
}

// Utility function to escape HTML to prevent XSS
function escapeHtml(unsafe) {
    if (unsafe === null || typeof unsafe === 'undefined') return '';
    return String(unsafe)
         .replace(/&/g, "&amp;")
         .replace(/</g, "&lt;")
         .replace(/>/g, "&gt;")
         .replace(/"/g, "&quot;")
         .replace(/'/g, "&#039;");
}

// Create HTML content for the step details modal
function createStepDetailsModalHtml(testCase) {
    return `
        <div class="modal-header">
            <h5 class="modal-title">Step Details: ${escapeHtml(testCase.tc_id || testCase.id)}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
            <p><strong>Description:</strong> ${escapeHtml(testCase.description || testCase.name || '-')}</p>
            <p><strong>Status:</strong> ${escapeHtml(testCase.status || 'Unknown')}</p>
            ${testCase.error_message ? `<p><strong>Error Message:</strong> <pre>${escapeHtml(testCase.error_message)}</pre></p>` : ''}
            ${testCase.input_data ? `<p><strong>Input Data:</strong> <pre style="white-space: pre-wrap; word-wrap: break-word;">${escapeHtml(testCase.input_data)}</pre></p>` : ''}
            ${testCase.output_data ? `<p><strong>Output Data:</strong> <pre style="white-space: pre-wrap; word-wrap: break-word;">${escapeHtml(testCase.output_data)}</pre></p>` : ''}
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        </div>
    `;
}

// Show the step details modal
function showStepDetailsModal(testCase) {
    let modalElement = document.getElementById('stepDetailsModal');

    if (!modalElement) {
        modalElement = document.createElement('div');
        modalElement.id = 'stepDetailsModal';
        modalElement.className = 'modal fade';
        modalElement.setAttribute('tabindex', '-1');
        modalElement.setAttribute('aria-labelledby', 'stepDetailsModalLabel');
        modalElement.setAttribute('aria-hidden', 'true');

        const modalDialog = document.createElement('div');
        modalDialog.className = 'modal-dialog modal-lg modal-dialog-scrollable'; // Added modal-lg and modal-dialog-scrollable

        const modalContent = document.createElement('div');
        modalContent.className = 'modal-content';

        modalDialog.appendChild(modalContent);
        modalElement.appendChild(modalDialog);
        document.body.appendChild(modalElement);
    }

    const modalContent = modalElement.querySelector('.modal-content');
    if (modalContent) {
        modalContent.innerHTML = createStepDetailsModalHtml(testCase);
    }

    // Ensure Bootstrap 5 modal instance is correctly created and shown
    const modal = new bootstrap.Modal(modalElement);
    modal.show();
}

// Display failure context modal
function displayFailureContextModal(failureContext, testCaseName) {
    let modalElement = document.getElementById('failureContextModal');

    if (!modalElement) {
        modalElement = document.createElement('div');
        modalElement.id = 'failureContextModal';
        modalElement.className = 'modal fade';
        modalElement.setAttribute('tabindex', '-1');
        modalElement.setAttribute('aria-labelledby', 'failureContextModalLabel');
        modalElement.setAttribute('aria-hidden', 'true');

        const modalDialog = document.createElement('div');
        modalDialog.className = 'modal-dialog modal-lg modal-dialog-scrollable'; // Added modal-lg and modal-dialog-scrollable

        const modalContent = document.createElement('div');
        modalContent.className = 'modal-content';

        modalDialog.appendChild(modalContent);

        document.body.appendChild(modalElement);
    }

    const modalContent = modalElement.querySelector('.modal-content');
    if (modalContent) {
        modalContent.innerHTML = `
            <div class="modal-header">
                <h5 class="modal-title">Failure Context: ${testCaseName}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <pre style="white-space: pre-wrap; word-wrap: break-word;">${escapeHtml(failureContext)}</pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        `;
    }

    // Ensure Bootstrap 5 modal instance is correctly created and shown
    const modal = new bootstrap.Modal(modalElement);
    modal.show();
}

// Update counters
function updateCounters() {
    try {
        // Skip if no counter elements
        if (!document.getElementById('total-count')) {
            return;
        }

        // Use totalRecordsForActiveTimeRange for the 'Total' count
        // Fallback to current reports length if totalRecordsForActiveTimeRange is 0 (e.g., initial state before fetch)
        const totalCount = currentState.totalRecordsForActiveTimeRange > 0 ?
                           currentState.totalRecordsForActiveTimeRange :
                           (currentState.reports ? currentState.reports.length : 0);

        document.getElementById('total-count').textContent = totalCount;

        // Passed, Failed, Running counts are derived from the currently displayed/cached reports
        let passedCount = 0;
        let failedCount = 0;
        let runningCount = 0;

        (currentState.reports || []).forEach(report => {
            const status = (report.status || '').toLowerCase();
            if (status === 'passed' || status === 'success') {
                passedCount++;
            } else if (status === 'failed') {
                failedCount++;
            } else if (status === 'running' || status === 'queued') {
                runningCount++;
            }
        });

        document.getElementById('passed-count').textContent = passedCount;
        document.getElementById('failed-count').textContent = failedCount;
        document.getElementById('running-count').textContent = runningCount;
    } catch (error) {
        console.error('Error updating counters:', error);
    }
}

/**
 * Handles the click event for the 'Rerun Test Case' button.
 * @param {string} tcId - The Test Case ID to rerun.
 * @param {string} tsnId - The parent Test Session ID for context.
 */
async function handleRerunTestCaseClick(tcId, tsnId) {
    console.log(`Attempting to rerun test case ${tcId} from session ${tsnId}`);
    const credentials = window.reportsCredentials.get();
    if (!credentials) {
        window.reportsCredentials.onAvailable(() => handleRerunTestCaseClick(tcId, tsnId));
        return;
    }

    // Retrieve original parameters from the currently displayed test details
    const originalParameters = currentState.currentTestDetails?.originalParameters || {};
    console.log('Using original parameters for rerun:', originalParameters);

    // Optional: Disable button to prevent multiple clicks
    const rerunButton = document.querySelector(`button.rerun-tc-btn[data-tc-id='${tcId}'][data-tsn-id='${tsnId}']`);
    if (rerunButton) {
        rerunButton.disabled = true;
        rerunButton.textContent = 'Rerunning...';
    }

    try {
        const result = await triggerTestCaseRerun(tcId, tsnId, credentials.uid, credentials.password, originalParameters);
        if (result.success) {
            alert(`Test case ${tcId} submitted for rerun. New Test Session ID: ${result.new_tsn_id || 'N/A'}`);
            console.log('Rerun successful for TC ${tcId}. Response:', result);
            // Optionally, refresh or redirect, or update UI to show new run
        } else {
            alert(`Failed to rerun test case ${tcId}: ${result.message || 'Unknown error'}`);
            console.error(`Rerun failed for TC ${tcId}. Response:`, result);
        }
    } catch (error) {
        alert(`An error occurred while trying to rerun test case ${tcId}: ${error.message}`);
        console.error(`Rerun error for TC ${tcId}:`, error);
    } finally {
        if (rerunButton) {
            rerunButton.disabled = false;
            rerunButton.textContent = 'Rerun';
        }
    }
}

/**
 * Triggers the backend API to rerun a specific test case.
 * @param {string} tcId - The Test Case ID.
 * @param {string} tsnId - The parent Test Session ID (for context).
 * @param {string} uid - User ID.
 * @param {string} password - User Password.
 * @param {object} originalParameters - The original parameters from the parent test suite run.
 * @returns {Promise<object>} - The API response.
 */
