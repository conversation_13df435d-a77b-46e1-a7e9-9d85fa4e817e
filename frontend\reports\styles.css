/* Reports Page Styles */

/* Global styles */
:root {
  --primary-color: #0078d4;
  --secondary-color: #106ebe;
  --success-color: #107c10;
  --danger-color: #d83b01;
  --warning-color: #ffb900;
  --light-color: #f3f2f1;
  --dark-color: #323130;
  --border-color: #edebe9;
  --card-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  --teams-primary: #464775;
  --teams-light: #ffffff;
  --teams-border: #e1dfdd;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  color: var(--dark-color);
}

/* Header and navigation styles */
.header {
  background-color: var(--primary-color);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Dashboard-style Header */
.ms-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--teams-primary);
  color: var(--teams-light);
  padding: 0.75rem 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.ms-header-brand {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--teams-light);
  text-decoration: none;
}

.ms-header-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.ms-environment-display {
  font-size: 0.875rem;
  color: var(--teams-light);
}

.ms-user-info {
  font-size: 0.875rem;
  color: var(--teams-light);
}

.ms-Button {
  background-color: transparent;
  border: 1px solid var(--teams-light);
  color: var(--teams-light);
  padding: 0.375rem 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s ease;
}

.ms-Button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.ms-Button-label {
  font-weight: 400;
}

.navbar-brand {
  font-weight: bold;
  color: white !important;
}

.nav-link {
  color: rgba(255, 255, 255, 0.8) !important;
  transition: color 0.2s ease;
}

.nav-link:hover {
  color: white !important;
}

.nav-link.active {
  color: white !important;
  font-weight: 600;
  border-bottom: 2px solid white;
}

/* Sidebar styles */
.sidebar {
  background-color: white;
  border-right: 1px solid var(--border-color);
  height: calc(100vh - 56px);
}

.sidebar .nav-link {
  color: var(--dark-color) !important;
  border-radius: 0;
  padding: 10px 15px;
  margin: 2px 0;
}

.sidebar .nav-link:hover {
  background-color: #f5f5f5;
  color: var(--primary-color) !important;
}

.sidebar .nav-link.active {
  background-color: #f0f0f0;
  color: var(--primary-color) !important;
  font-weight: bold;
  border-left: 3px solid var(--primary-color);
  border-bottom: none;
}

/* Main content styles */
.main-content {
  padding: 20px;
}

.card {
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
}

.card-header {
  background-color: #f9f9f9;
  border-bottom: 1px solid var(--border-color);
  font-weight: 600;
}

/* Dashboard stats styles */
.stat-card {
  padding: 1.5rem 2rem;
  border-radius: 0.5rem; /* Consistent rounded corners */
  box-shadow: var(--card-shadow); /* Use existing shadow variable */
  text-align: center;
  transition: transform 0.2s ease-in-out;
  margin-bottom: 1.5rem; /* Spacing for responsive layouts */
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-value {
  font-size: 2.5rem; /* Slightly larger for more emphasis */
  font-weight: bold;
  line-height: 1.2;
  display: block; /* Ensures it takes its own line */
}

.stat-title {
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  opacity: 0.9;
  margin-bottom: 0.5rem;
  display: block; /* Ensures it takes its own line */
  color: inherit; /* Inherits color from parent (.text-white or default) */
}

/* Filter and control styles */
.filter-bar {
  background-color: white;
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  box-shadow: var(--card-shadow);
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

/* Table styles */
.table {
  background-color: white;
}

.table th {
  border-top: none;
  background-color: #f9f9f9;
  font-weight: 600;
}

.table tbody tr {
  transition: background-color 0.2s;
}

.table tbody tr:hover {
  background-color: #f5f5f5;
}

/* FixedHeader styles */
.fixedHeader-floating {
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000 !important;
}

.fixedHeader-floating th {
  background-color: #f9f9f9;
  padding: 10px !important;
}

/* SearchPanes styles - customized to match our original filter controls */
.dtsp-panes {
  padding: 0 !important;
  margin: 0 !important;
}

/* Make SearchPanes look like our original filter controls */
.dtsp-searchPane {
  margin-bottom: 0 !important;
}

/* Style the SearchPane headers like our original filter labels */
.dtsp-searchPane .dtsp-titleRow {
  background-color: transparent !important;
  border: none !important;
  padding: 0 0 5px 0 !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  color: #212529 !important;
}

/* Style the SearchPane containers like our original select boxes */
.dtsp-searchPane .dtsp-subRow2 {
  background-color: white !important;
  border: 1px solid #ced4da !important;
  border-radius: 0.25rem !important;
  padding: 0.25rem !important;
}

/* Style the SearchPane count badges */
.dtsp-pill {
  background-color: #f8f9fa !important;
  color: #6c757d !important;
  border: 1px solid #dee2e6 !important;
  font-weight: normal !important;
}

/* Style the selected items */
.dtsp-selected .dtsp-pill {
  background-color: var(--primary-color) !important;
  color: white !important;
  border-color: var(--primary-color) !important;
}

/* Make the SearchPanes container fit our layout */
#searchPanes-container .dtsp-panesContainer {
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Adjust the columns layout to match our original 3-column layout */
.dtsp-columns-3 {
  grid-template-columns: repeat(3, 1fr) !important;
  gap: 1rem !important;
}

/* Animations for table row updates */
.report-row {
    transition: opacity 0.3s ease-in-out, background-color 0.5s ease-in-out;
}

.report-row.new-row {
    opacity: 0;
    animation: highlightFade 3s forwards;
}

.report-row.new-row.visible {
    opacity: 1;
}

@keyframes highlightFade {
    0% { background-color: rgba(255, 247, 204, 0.7); }
    100% { background-color: transparent; }
}

/* Two-line row structure for test cases table */
#test-cases-table .test-case-description-row td {
    padding-top: 0 !important;
    padding-bottom: 0.5rem;
    border-top: none;
}

#test-cases-table .test-case-main-row + .test-case-description-row {
    border-top: none;
}

#test-cases-table .test-case-description-row:hover {
    background-color: inherit;
}

#test-cases-table .test-case-main-row:hover + .test-case-description-row {
    background-color: rgba(0, 0, 0, 0.05);
}

/* Ensure table rows have a bit of spacing for better readability */
.table-striped tbody tr {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* Status colors */
.text-success {
  color: var(--success-color) !important;
}

.text-danger {
  color: var(--danger-color) !important;
}

.text-warning {
  color: var(--warning-color) !important;
}

.bg-success {
  background-color: var(--success-color) !important;
}

.bg-danger {
  background-color: var(--danger-color) !important;
}

.bg-warning {
  background-color: var(--warning-color) !important;
}

/* Progress bar styles */
.progress {
  height: 8px;
  margin-bottom: 5px;
}

.progress-bar {
  background-color: var(--success-color);
}

/* Test details section */
.test-details-section {
  background-color: white;
  border-radius: 4px;
  box-shadow: var(--card-shadow);
  margin-top: 30px;
  padding: 20px;
}

.detail-label {
  font-weight: 600;
  margin-bottom: 5px;
  color: #666;
}

.detail-value {
  font-size: 1rem;
  margin-bottom: 15px;
}

/* Chart containers */
.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

/* Loading indicator */
.loading-spinner {
  width: 3rem;
  height: 3rem;
}

/* Custom button styles */
.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .sidebar {
    position: static;
    height: auto;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }

  .chart-container {
    height: 200px;
  }

  .stats-value {
    font-size: 1.5rem;
  }
}

/* Animation for refreshing data */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  animation: fadeIn 0.5s;
}

/* Custom dropdown styles */
.dropdown-menu {
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
}

.dropdown-item {
  padding: 8px 15px;
}

.dropdown-item:hover {
  background-color: #f5f5f5;
  color: var(--primary-color);
}

.dropdown-item.active {
  background-color: var(--primary-color);
  color: white;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #aaa;
}

/* Notifications */
.refresh-alert {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    opacity: 0;
    animation: fadeInOut 3s forwards;
    border-radius: 4px;
    padding: 12px 16px;
    margin-bottom: 8px;
}

.refresh-success-alert {
    background-color: #e6f4ea;
    border-left: 4px solid var(--success-color);
    color: #0d652d;
}

.refresh-error-alert {
    background-color: #fdecea;
    border-left: 4px solid var(--danger-color);
    color: #8e2924;
}

.refresh-warning-alert {
    background-color: #fff8e6;
    border-left: 4px solid var(--warning-color);
    color: #8a5700;
}

.refresh-info-alert {
    background-color: #e8f1fa;
    border-left: 4px solid var(--primary-color);
    color: #0c559d;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translateY(-20px); }
    10% { opacity: 1; transform: translateY(0); }
    90% { opacity: 1; transform: translateY(0); }
    100% { opacity: 0; transform: translateY(-20px); }
}

/* Loading indicator animation */
.loading-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.loading-indicator .dots {
    display: flex;
    gap: 4px;
}

.loading-indicator .dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #0d6efd;
    animation: bounce 1.4s infinite ease-in-out both;
}

.loading-indicator .dot:nth-child(1) {
    animation-delay: -0.32s;
}

.loading-indicator .dot:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes bounce {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

/* Custom Accordion Styles for Test Cases Table (Teams-like) */
#test-cases-table .accordion-item {
    border: none; /* Remove default accordion item border */
    background-color: transparent; /* Ensure it inherits cell background */
}

#test-cases-table .accordion-button {
    background-color: transparent;
    color: var(--primary-color); /* Use primary color for the text */
    border: none;
    box-shadow: none;
    padding: 0.5rem 0.75rem; /* Adjust padding as needed */
    text-align: left;
    width: 100%;
    font-weight: normal;
}

#test-cases-table .accordion-button:not(.collapsed) {
    color: var(--primary-color);
    background-color: transparent; /* Keep transparent when expanded */
    box-shadow: none;
    font-weight: 600; /* Make text bolder when expanded */
}

#test-cases-table .accordion-button:focus {
    z-index: 3;
    border-color: transparent; /* Remove focus border if not desired */
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb, 0), var(--bs-primary-rgb, 120), var(--bs-primary-rgb, 212), 0.25); /* Optional: Bootstrap-like focus ring, adjust color. Using fallback for --bs-primary-rgb */
}

#test-cases-table .accordion-button:hover {
    background-color: rgba(0, 0, 0, 0.03); /* Subtle hover background */
    text-decoration: underline;
}

/* Ensure the icon color matches the button text */
#test-cases-table .accordion-button::after {
    filter: brightness(0) saturate(100%) invert(38%) sepia(99%) saturate(1559%) hue-rotate(188deg) brightness(95%) contrast(101%); /* This filter makes the default Bootstrap icon use --primary-color. */
}

#test-cases-table .accordion-button:not(.collapsed)::after {
     filter: brightness(0) saturate(100%) invert(38%) sepia(99%) saturate(1559%) hue-rotate(188deg) brightness(95%) contrast(101%);
}


#test-cases-table .accordion-body {
    padding: 1rem;
    background-color: #ffffff; /* White background for content */
    border-top: 1px solid var(--border-color);
    border-left: 3px solid var(--primary-color);
    margin-top: 2px;
}

#test-cases-table .accordion-body ul {
    list-style-type: none;
    padding-left: 0;
}

#test-cases-table .accordion-body ul li {
    padding: 0.25rem 0;
    border-bottom: 1px dashed var(--border-color);
}

#test-cases-table .accordion-body ul li:last-child {
    border-bottom: none;
}

/* Adjustments for rows with specific backgrounds (e.g., failed tests) */
#test-cases-table tr.table-danger .accordion-button,
#test-cases-table td.bg-danger-subtle .accordion-button { 
    color: var(--danger-color);
}

#test-cases-table tr.table-danger .accordion-button::after,
#test-cases-table td.bg-danger-subtle .accordion-button::after {
     filter: brightness(0) saturate(100%) invert(26%) sepia(71%) saturate(2786%) hue-rotate(340deg) brightness(90%) contrast(90%); /* Filter for danger color */
}

#test-cases-table tr.table-danger .accordion-button:not(.collapsed),
#test-cases-table td.bg-danger-subtle .accordion-button:not(.collapsed) {
    color: var(--danger-color);
    font-weight: 600;
}

#test-cases-table tr.table-danger .accordion-body,
#test-cases-table td.bg-danger-subtle .accordion-body {
    border-left-color: var(--danger-color);
}