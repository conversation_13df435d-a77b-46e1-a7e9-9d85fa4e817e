/**
 * reports-auth.js
 *
 * Handles authentication for the reports module using the unified auth client.
 * Provides a login modal and logout button that integrate with the shared
 * authentication system.
 */

(function() {
    // DOM elements used by the authentication UI
    const elements = {
        loginModal: document.getElementById('reports-login-modal'),
        loginForm: document.getElementById('reports-login-form'),
        loginButton: document.getElementById('reports-login-button'),
        logoutButton: document.getElementById('reports-logout-button'),
        usernameInput: document.getElementById('reports-username'),
        passwordInput: document.getElementById('reports-password'),
        loginStatus: document.getElementById('reports-login-status'),
        userDisplay: document.getElementById('reports-user-display')
    };

    let unifiedAuthClient = null;
    let modalInstance = null;

    /**
     * Initialize unified auth client when available
     */
    function initUnifiedAuth() {
        if (window.unifiedAuthClient && !unifiedAuthClient) {
            unifiedAuthClient = window.unifiedAuthClient;
            unifiedAuthClient.addEventListener('sessionExpired', handleSessionExpired);
            console.log('✅ Reports connected to unified auth client');

            // Check for existing session using session manager
            if (window.sessionManager) {
                const session = window.sessionManager.getCurrentSession();
                if (session) {
                    console.log('✅ Reports: Found existing session for', session.user.uid);
                    updateLoginUI(true, session.user.uid);
                    return true;
                }
            }

            return true;
        }
        return false;
    }

    /**
     * Set up event listeners and initial state
     */
    function initializeAuth() {
        initUnifiedAuth();

        if (elements.loginModal) {
            modalInstance = new bootstrap.Modal(elements.loginModal);
        }

        elements.loginButton?.addEventListener('click', showLoginModal);
        elements.logoutButton?.addEventListener('click', handleLogout);
        elements.loginForm?.addEventListener('submit', handleLoginSubmit);

        if (unifiedAuthClient && unifiedAuthClient.isAuthenticated) {
            const currentUser = unifiedAuthClient.getCurrentUser();
            updateLoginUI(true, currentUser.uid);
        } else {
            updateLoginUI(false);
            showLoginModal();
        }
    }

    /** Show the login modal */
    function showLoginModal() {
        modalInstance?.show();
        elements.usernameInput?.focus();
    }

    /** Hide the login modal */
    function hideLoginModal() {
        modalInstance?.hide();
    }

    /** Handle login form submission */
    async function handleLoginSubmit(event) {
        event.preventDefault();
        const username = elements.usernameInput?.value;
        const password = elements.passwordInput?.value;

        if (!username || !password) {
            if (elements.loginStatus) {
                elements.loginStatus.textContent = 'Username and password are required.';
                elements.loginStatus.style.display = 'block';
            }
            return;
        }

        elements.loginStatus && (elements.loginStatus.style.display = 'none');

        try {
            if (unifiedAuthClient) {
                const result = await unifiedAuthClient.login(username, password);
                if (!result.success) {
                    if (elements.loginStatus) {
                        elements.loginStatus.textContent = result.error || 'Invalid credentials.';
                        elements.loginStatus.style.display = 'block';
                    }
                }
                // On success, handleAuthLogin will run via event listener
            }
        } catch (error) {
            console.error('Login error:', error);
            if (elements.loginStatus) {
                elements.loginStatus.textContent = 'An unexpected error occurred during login.';
                elements.loginStatus.style.display = 'block';
            }
        }
    }

    /** Handle user logout */
    async function handleLogout() {
        try {
            if (unifiedAuthClient) {
                await unifiedAuthClient.logout();
                // handleAuthLogout will run via event listener
            }
        } catch (error) {
            console.error('Logout error:', error);
        }
    }

    /** Respond to auth login events */
    function handleAuthLogin(data) {
        const user = data.user;
        updateLoginUI(true, user.uid);
        hideLoginModal();
        document.dispatchEvent(new CustomEvent('auth-state-changed', { detail: { loggedIn: true } }));
        console.log('✅ Reports: User authenticated via unified auth client');
    }

    /** Respond to auth logout events */
    function handleAuthLogout() {
        updateLoginUI(false);
        showLoginModal();
        document.dispatchEvent(new CustomEvent('auth-state-changed', { detail: { loggedIn: false } }));
        console.log('✅ Reports: User logged out via unified auth client');
    }

    /** Handle session expiration */
    function handleSessionExpired() {
        updateLoginUI(false);
        showLoginModal();
        document.dispatchEvent(new CustomEvent('auth-state-changed', { detail: { loggedIn: false } }));
        console.log('⏰ Reports: Session expired');
    }

    /**
     * Format user email to display name (extract name part before @)
     */
    function formatUserEmail(email) {
        if (!email || typeof email !== 'string') return 'Unknown';
        return email.split('@')[0];
    }

    /**
     * Update UI based on login state
     */
    function updateLoginUI(isLoggedIn, username = '') {
        if (isLoggedIn) {
            elements.loginButton && (elements.loginButton.style.display = 'none');
            elements.logoutButton && (elements.logoutButton.style.display = 'inline-block');
            if (elements.userDisplay) {
                const formattedName = formatUserEmail(username);
                elements.userDisplay.textContent = `Logged in as: ${formattedName}`;
            }
        } else {
            elements.loginButton && (elements.loginButton.style.display = 'inline-block');
            elements.logoutButton && (elements.logoutButton.style.display = 'none');
            if (elements.userDisplay) {
                elements.userDisplay.textContent = 'Not logged in';
            }
        }
    }

    // Global auth event listeners
    window.addEventListener('auth:login', (e) => handleAuthLogin(e.detail));
    window.addEventListener('auth:logout', () => handleAuthLogout());
    window.addEventListener('auth:token-refresh', () => console.log('Reports: token refreshed'));

    document.addEventListener('DOMContentLoaded', initializeAuth);
})();

