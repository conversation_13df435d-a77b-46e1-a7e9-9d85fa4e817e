function calculateDuration(startTime, endTime) {
    if (!startTime || !endTime) return 'N/A';

    try {
        // Create date objects from the strings
        const startDate = new Date(startTime);
        const endDate = new Date(endTime);

        // Check if these are valid dates
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            return 'Invalid date';
        }

        // Calculate the difference in milliseconds
        const diffMs = endDate - startDate;

        // Convert to seconds, minutes, hours
        const seconds = Math.floor((diffMs / 1000) % 60);
        const minutes = Math.floor((diffMs / (1000 * 60)) % 60);
        const hours = Math.floor(diffMs / (1000 * 60 * 60));

        // Format the duration
        if (hours > 0) {
            return `${hours}h ${minutes}m ${seconds}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds}s`;
        } else {
            return `${seconds}s`;
        }
    } catch (error) {
        console.error(`Error calculating duration: ${error}`);
        return 'Error';
    }
}

/**
 * Initialize the bulk rerun UI by adding the Select All checkbox and Rerun Selected button
 */
function initializeBulkRerunUI() {
    // Check if already initialized
    if (currentState.bulkRerunUIInitialized || document.getElementById('rerunSelectedCasesBtnGlobal')) {
        return;
    }
    
    console.log('Initializing bulk rerun UI...');
    
    // Create the Rerun Selected button
    const rerunBtn = document.createElement('button');
    rerunBtn.id = 'rerunSelectedCasesBtnGlobal';
    rerunBtn.textContent = 'Rerun Selected';
    rerunBtn.className = 'btn btn-primary mb-2';
    rerunBtn.disabled = true; // Initially disabled
    rerunBtn.onclick = handleRerunSelectedClick;
    
    // Get the test cases table
    const table = document.getElementById('test-cases-table');
    if (!table) {
        console.error('Test cases table not found. Cannot initialize bulk rerun UI.');
        return;
    }
    
    // Insert the button before the table
    table.parentNode.insertBefore(rerunBtn, table);
    
    // Add Select All checkbox to the table header
    const thead = table.querySelector('thead');
    if (!thead) {
        console.error('Test cases table header not found. Cannot add Select All checkbox.');
        return;
    }
    
    // Get the header row
    const headerRow = thead.querySelector('tr');
    if (!headerRow) {
        console.error('Test cases table header row not found. Cannot add Select All checkbox.');
        return;
    }
    
    // Create the header cell with the Select All checkbox
    const selectAllTh = document.createElement('th');
    selectAllTh.scope = 'col';
    selectAllTh.className = 'text-center';
    const selectAllCheckbox = document.createElement('input');
    selectAllCheckbox.type = 'checkbox';
    selectAllCheckbox.id = 'selectAllTestCasesCheckbox';
    selectAllCheckbox.title = 'Select/Deselect All';
    selectAllCheckbox.className = 'form-check-input';
    selectAllCheckbox.onchange = handleSelectAllCheckboxChange;
    selectAllTh.appendChild(selectAllCheckbox);
    
    // Insert the Select All checkbox as the first column
    headerRow.insertBefore(selectAllTh, headerRow.firstChild);
    
    // Remove the Actions column header if it exists
    const actionsTh = headerRow.querySelector('th:last-child');
    if (actionsTh && actionsTh.textContent.trim() === 'Actions') {
        headerRow.removeChild(actionsTh);
    }
    
    // Update the flag to indicate that the UI has been initialized
    currentState.bulkRerunUIInitialized = true;
    console.log('Bulk rerun UI initialized successfully');
}

/**
 * Handle the Select All checkbox change event
 * @param {Event} event - The change event
 */
function handleSelectAllCheckboxChange(event) {
    const isChecked = event.target.checked;
    const checkboxes = document.querySelectorAll('#test-cases-table tbody .test-case-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = isChecked;
    });
    
    updateBulkRerunButtonState();
}

/**
 * Handle individual row checkbox change event
 */
function handleRowCheckboxChange() {
    updateBulkRerunButtonState();
}

/**
 * Update the state of the Rerun Selected button based on checkbox selections
 */
function updateBulkRerunButtonState() {
    const rerunBtn = document.getElementById('rerunSelectedCasesBtnGlobal');
    const selectAllCheckbox = document.getElementById('selectAllTestCasesCheckbox');
    const rowCheckboxes = document.querySelectorAll('#test-cases-table tbody .test-case-checkbox');
    
    if (!rerunBtn || !selectAllCheckbox) {
        return;
    }
    
    let checkedCount = 0;
    rowCheckboxes.forEach(checkbox => {
        if (checkbox.checked) {
            checkedCount++;
        }
    });
    
    rerunBtn.disabled = (checkedCount === 0);
    
    if (rowCheckboxes.length > 0) {
        selectAllCheckbox.checked = (checkedCount === rowCheckboxes.length);
        selectAllCheckbox.indeterminate = (checkedCount > 0 && checkedCount < rowCheckboxes.length);
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    }
}

/**
 * Handle the Rerun Selected button click event
 */
async function handleRerunSelectedClick() {
    // Collect all selected test cases
    const selectedCasesToRerun = [];
    const selectedCheckboxes = document.querySelectorAll('#test-cases-table tbody .test-case-checkbox:checked');
    
    // Create a map to store row references by tc_id for UI updates
    const rowMap = new Map();
    
    selectedCheckboxes.forEach(checkbox => {
        const tc_id = checkbox.dataset.tcId;
        const tsn_id = checkbox.dataset.tsnId;
        
        // Store the row reference for later UI updates
        const row = checkbox.closest('tr');
        if (row) {
            rowMap.set(tc_id, row);
        }
        
        selectedCasesToRerun.push({ tc_id, tsn_id });
    });
    
    // Handle no selection
    if (selectedCasesToRerun.length === 0) {
        alert('No test cases selected for rerun.');
        return;
    }
    
    // UI feedback (start)
    const rerunBtn = document.getElementById('rerunSelectedCasesBtnGlobal');
    if (rerunBtn) {
        rerunBtn.disabled = true;
        rerunBtn.textContent = `Rerunning ${selectedCasesToRerun.length} test cases...`;
    }
    
    const credentials = window.reportsCredentials.get();
    if (!credentials) {
        window.reportsCredentials.onAvailable(() => handleRerunSelectedClick());
        if (rerunBtn) {
            rerunBtn.disabled = false;
            rerunBtn.textContent = 'Rerun Selected';
        }
        return;
    }
    
    // Get original parameters from the parent test suite
    const originalParametersFromSuite = currentState.currentTestDetails?.originalParameters || {};
    console.log('[DEBUG] handleRerunSelectedClick: Using original parameters for bulk rerun:', originalParametersFromSuite);
    
    // Update status text for each test case row (without spinner)
    selectedCasesToRerun.forEach(tc => {
        const row = rowMap.get(tc.tc_id);
        if (row) {
            // Update status text in the status cell (3rd cell)
            const statusCell = row.cells[2]; // Adjust index if needed based on your table structure
            if (statusCell) {
                // Store original status for restoration if needed
                statusCell.dataset.originalContent = statusCell.innerHTML;
                statusCell.innerHTML = '<span class="text-primary">Preparing to rerun...</span>';
            }
        }
    });
    
    // Perform the reruns
    let successCount = 0;
    let failCount = 0;
    const results = [];
    let hasConnectionError = false;
    
    // Track the test cases that need status polling
    const activeTestRuns = new Map();
    
    for (const tc of selectedCasesToRerun) {
        try {
            console.log(`Rerunning test case ${tc.tc_id} from session ${tc.tsn_id}...`);
            const result = await triggerTestCaseRerun(tc.tc_id, tc.tsn_id, credentials.uid, credentials.password, originalParametersFromSuite);
            
            // Update the row status regardless of result
            const row = rowMap.get(tc.tc_id);
            const statusCell = row?.cells[2];
            
            if (result.success) {
                successCount++;
                
                // Store the new session ID for status polling
                if (result.new_tsn_id) {
                    activeTestRuns.set(result.new_tsn_id, {
                        tc_id: tc.tc_id,
                        status: 'Running',
                        rowElement: row
                    });
                }
                
                results.push({
                    tc_id: tc.tc_id,
                    success: true,
                    new_tsn_id: result.new_tsn_id || 'N/A'
                });
                console.log(`Rerun successful for TC ${tc.tc_id}. New TSN ID: ${result.new_tsn_id || 'N/A'}`);
                
                // Update status cell to show success
                if (statusCell) {
                    statusCell.innerHTML = '<span class="text-success">Submitted for Rerun</span>';
                }
            } else {
                failCount++;
                results.push({
                    tc_id: tc.tc_id,
                    success: false,
                    error: result.message || 'Unknown error'
                });
                console.error(`Rerun failed for TC ${tc.tc_id}. Response:`, result);
                
                // Update status cell to show failure
                if (statusCell) {
                    statusCell.innerHTML = '<span class="text-danger">Rerun Failed</span>';
                }
            }
        } catch (error) {
            failCount++;
            results.push({
                tc_id: tc.tc_id,
                success: false,
                error: error.message
            });
            console.error(`Error rerunning TC ${tc.tc_id}:`, error);
            
            // Check for connection errors
            if (error.message && (error.message.includes('Not connected') || error.message.includes('Network Error'))) {
                hasConnectionError = true;
            }
            
            // Update status cell to show error
            const row = rowMap.get(tc.tc_id);
            const statusCell = row?.cells[2];
            if (statusCell) {
                statusCell.innerHTML = `<span class="text-danger">Error: ${error.message.substring(0, 30)}${error.message.length > 30 ? '...' : ''}</span>`;
            }
        }
    }
    
    // UI feedback (end)
    if (hasConnectionError) {
        alert(`Database connection error detected. Some test cases may not have been rerun properly. Successful: ${successCount}, Failed: ${failCount}.`);
    } else {
        alert(`Rerun process completed. Successful: ${successCount}, Failed: ${failCount}. Status will be updated automatically.`);
    }
    
    // Reset UI
    if (rerunBtn) {
        rerunBtn.disabled = false;
        rerunBtn.textContent = 'Rerun Selected';
    }
    
    // Uncheck all checkboxes
    document.getElementById('selectAllTestCasesCheckbox').checked = false;
    document.querySelectorAll('#test-cases-table tbody .test-case-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    
    updateBulkRerunButtonState();
    
    // Start polling for test status updates if we have active test runs
    if (activeTestRuns.size > 0) {
        startTestStatusPolling(activeTestRuns);
    }
}

/**
 * Start polling for test status updates
 * @param {Map} activeTestRuns - Map of active test runs with test session IDs as keys
 * @param {Object} credentials - User credentials for API calls
 */
