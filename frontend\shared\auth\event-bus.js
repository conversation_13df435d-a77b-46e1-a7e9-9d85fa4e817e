/**
 * Cross-module authentication event bus
 * Uses window events, localStorage, and BroadcastChannel to
 * synchronize authentication state across modules and browser tabs.
 */
(function(global) {
  const STORAGE_KEY = 'smarttest_auth';
  const CHANNEL_NAME = 'smarttest-auth';

  const channel = typeof BroadcastChannel !== 'undefined'
    ? new BroadcastChannel(CHANNEL_NAME)
    : null;

  function persist(type, detail) {
    try {
      if (type === 'login' || type === 'token-refresh') {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(detail));
      } else if (type === 'logout') {
        localStorage.removeItem(STORAGE_KEY);
      }
    } catch (err) {
      console.warn('Auth event bus storage failed:', err);
    }
  }

  function broadcast(type, detail) {
    if (!channel) return;
    try {
      channel.postMessage({ type, detail });
    } catch (err) {
      console.warn('Auth event bus broadcast failed:', err);
    }
  }

  function dispatch(type, detail = {}) {
    persist(type, detail);
    broadcast(type, detail);
    global.dispatchEvent(new CustomEvent(`auth:${type}`, { detail }));
  }

  if (channel) {
    channel.addEventListener('message', (event) => {
      const { type, detail } = event.data || {};
      if (!type) return;
      persist(type, detail);
      global.dispatchEvent(new CustomEvent(`auth:${type}`, { detail }));
    });
  }

  // Emit login event on load if auth state exists
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const detail = JSON.parse(stored);
      global.dispatchEvent(new CustomEvent('auth:login', { detail }));
    }
  } catch (err) {
    console.warn('Auth event bus initialization failed:', err);
  }

  global.authEventBus = { dispatch };
})(window);
