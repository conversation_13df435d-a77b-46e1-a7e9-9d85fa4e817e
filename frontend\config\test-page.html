<!DOCTYPE html>
<html>
<head>
    <title>Config Test Page</title>
</head>
<body>
    <h1>Config Module Test Page</h1>
    <p>If you can see this page, the Config module is being served correctly.</p>
    <p>Current URL: <span id="current-url"></span></p>
    <p>Timestamp: <span id="timestamp"></span></p>
    
    <script>
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('timestamp').textContent = new Date().toISOString();
        console.log('✅ Config test page loaded successfully');
        console.log('Current URL:', window.location.href);
        console.log('Timestamp:', new Date().toISOString());
    </script>
</body>
</html>
