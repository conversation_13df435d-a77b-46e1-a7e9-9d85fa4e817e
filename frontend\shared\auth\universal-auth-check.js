/**
 * Universal Authentication Check
 * Standardized authentication verification for all modules
 */
class UniversalAuthCheck {
    constructor(moduleName) {
        this.moduleName = moduleName;
        this.maxRetries = 5;
        this.retryDelay = 1000;
    }

    /**
     * Perform authentication check with retry logic
     */
    async performCheck() {
        console.log(`🔍 ${this.moduleName}: Starting authentication check`);

        for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
            try {
                console.log(`🔍 ${this.moduleName}: Auth check attempt ${attempt}/${this.maxRetries}`);
                
                const result = await this.checkAuthentication();
                
                if (result.isAuthenticated) {
                    console.log(`✅ ${this.moduleName}: Authentication successful for ${result.user.uid}`);
                    this.handleAuthSuccess(result);
                    return true;
                } else if (attempt === this.maxRetries) {
                    console.log(`❌ ${this.moduleName}: Authentication failed after ${this.maxRetries} attempts`);
                    this.handleAuthFailure();
                    return false;
                } else {
                    console.log(`⏳ ${this.moduleName}: Authentication not ready, retrying in ${this.retryDelay}ms`);
                    await this.delay(this.retryDelay);
                }
            } catch (error) {
                console.error(`❌ ${this.moduleName}: Auth check error on attempt ${attempt}:`, error);
                if (attempt === this.maxRetries) {
                    this.handleAuthFailure();
                    return false;
                }
                await this.delay(this.retryDelay);
            }
        }

        return false;
    }

    /**
     * Check authentication using centralized session manager
     */
    async checkAuthentication() {
        // Wait for session manager to be available
        if (!window.sessionManager) {
            throw new Error('Session manager not available');
        }

        // Check for existing session
        const session = window.sessionManager.getCurrentSession();
        
        if (session) {
            // Validate session with server
            const isValid = await this.validateSessionWithServer(session);
            
            if (isValid) {
                return {
                    isAuthenticated: true,
                    user: session.user,
                    token: session.token,
                    sessionId: session.sessionId
                };
            } else {
                // Session invalid, clear it
                window.sessionManager.clearSession();
                return { isAuthenticated: false };
            }
        }

        return { isAuthenticated: false };
    }

    /**
     * Validate session with server
     */
    async validateSessionWithServer(session) {
        try {
            const response = await fetch('/auth/validate', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${session.token}`
                },
                credentials: 'include'
            });

            return response.ok;
        } catch (error) {
            console.error(`❌ ${this.moduleName}: Session validation failed:`, error);
            return false;
        }
    }

    /**
     * Handle successful authentication
     */
    handleAuthSuccess(authResult) {
        // Update UI to show authenticated state
        this.updateAuthUI(authResult.user);
        
        // Set up API service credentials
        this.setupApiCredentials(authResult);
        
        // Hide any login modals
        this.hideLoginModal();
        
        // Dispatch success event
        window.dispatchEvent(new CustomEvent('auth:success', {
            detail: { module: this.moduleName, user: authResult.user }
        }));
    }

    /**
     * Handle authentication failure
     */
    handleAuthFailure() {
        // Clear any existing UI state
        this.clearAuthUI();
        
        // Show login modal (but don't redirect for Config module)
        if (this.moduleName !== 'Config') {
            this.showLoginModal();
        } else {
            // For Config module, show inline login instead of redirecting
            this.showInlineLogin();
        }
        
        // Dispatch failure event
        window.dispatchEvent(new CustomEvent('auth:failure', {
            detail: { module: this.moduleName }
        }));
    }

    /**
     * Update UI to show authenticated state
     */
    updateAuthUI(user) {
        // Update user display elements
        const userElements = document.querySelectorAll('[data-user-display]');
        userElements.forEach(element => {
            element.textContent = user.name || user.uid;
            element.classList.add('authenticated');
        });

        // Update auth status elements
        const statusElements = document.querySelectorAll('[data-auth-status]');
        statusElements.forEach(element => {
            element.textContent = `Logged in as: ${user.uid}`;
            element.classList.add('authenticated');
        });

        // Show authenticated sections
        const authSections = document.querySelectorAll('[data-require-auth]');
        authSections.forEach(section => {
            section.style.display = 'block';
        });
    }

    /**
     * Clear authentication UI
     */
    clearAuthUI() {
        const userElements = document.querySelectorAll('[data-user-display]');
        userElements.forEach(element => {
            element.textContent = 'Not Logged In';
            element.classList.remove('authenticated');
        });

        const authSections = document.querySelectorAll('[data-require-auth]');
        authSections.forEach(section => {
            section.style.display = 'none';
        });
    }

    /**
     * Set up API service credentials
     */
    setupApiCredentials(authResult) {
        if (window.apiService && typeof window.apiService.setCredentials === 'function') {
            window.apiService.setCredentials(authResult.user.uid, '');
            console.log(`✅ ${this.moduleName}: API credentials configured`);
        }

        if (window.unifiedApiService && typeof window.unifiedApiService.setAuthToken === 'function') {
            window.unifiedApiService.setAuthToken(authResult.token);
            console.log(`✅ ${this.moduleName}: Unified API auth token set`);
        }
    }

    /**
     * Show login modal
     */
    showLoginModal() {
        // Try different login modal implementations
        if (window.configAuth && typeof window.configAuth.showLoginModal === 'function') {
            window.configAuth.showLoginModal();
        } else if (window.dashboardAuth && typeof window.dashboardAuth.showLoginModal === 'function') {
            window.dashboardAuth.showLoginModal();
        } else {
            console.log(`⚠️ ${this.moduleName}: No login modal implementation found`);
        }
    }

    /**
     * Show inline login for Config module (avoid redirect)
     */
    showInlineLogin() {
        console.log(`🔑 ${this.moduleName}: Showing inline login`);
        
        // Create inline login form if it doesn't exist
        let loginContainer = document.getElementById('inline-login-container');
        if (!loginContainer) {
            loginContainer = document.createElement('div');
            loginContainer.id = 'inline-login-container';
            loginContainer.innerHTML = `
                <div class="inline-login-overlay">
                    <div class="inline-login-modal">
                        <h3>Login Required</h3>
                        <p>Please log in to access the Custom Test Runner</p>
                        <form id="inline-login-form">
                            <input type="email" id="inline-username" placeholder="Username" required>
                            <input type="password" id="inline-password" placeholder="Password" required>
                            <button type="submit">Login</button>
                            <button type="button" onclick="window.location.href='/dashboard/'">Go to Dashboard</button>
                        </form>
                        <div id="inline-login-error" style="color: red; display: none;"></div>
                    </div>
                </div>
            `;
            document.body.appendChild(loginContainer);
            
            // Set up login form handler
            this.setupInlineLoginHandler();
        }
        
        loginContainer.style.display = 'block';
    }

    /**
     * Set up inline login form handler
     */
    setupInlineLoginHandler() {
        const form = document.getElementById('inline-login-form');
        if (form) {
            form.addEventListener('submit', async (event) => {
                event.preventDefault();
                
                const username = document.getElementById('inline-username').value;
                const password = document.getElementById('inline-password').value;
                const errorDiv = document.getElementById('inline-login-error');
                
                try {
                    const response = await fetch('/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ uid: username, password }),
                        credentials: 'include'
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        // Store session
                        window.sessionManager.storeSession({
                            sessionId: result.sessionId,
                            user: result.user,
                            token: result.token,
                            expiresAt: result.expiresAt
                        });
                        
                        // Hide login form
                        this.hideInlineLogin();
                        
                        // Perform auth check again
                        this.performCheck();
                    } else {
                        errorDiv.textContent = result.message || 'Login failed';
                        errorDiv.style.display = 'block';
                    }
                } catch (error) {
                    errorDiv.textContent = 'Login error: ' + error.message;
                    errorDiv.style.display = 'block';
                }
            });
        }
    }

    /**
     * Hide inline login
     */
    hideInlineLogin() {
        const loginContainer = document.getElementById('inline-login-container');
        if (loginContainer) {
            loginContainer.style.display = 'none';
        }
    }

    /**
     * Hide login modal
     */
    hideLoginModal() {
        this.hideInlineLogin();
        
        if (window.configAuth && typeof window.configAuth.hideLoginModal === 'function') {
            window.configAuth.hideLoginModal();
        }
    }

    /**
     * Delay utility
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Export for use in modules
window.UniversalAuthCheck = UniversalAuthCheck;

// Export for CommonJS compatibility
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UniversalAuthCheck;
}