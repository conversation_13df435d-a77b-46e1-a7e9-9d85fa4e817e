/**
 * Unified API Service for SmartTest Application
 *
 * Combines functionality from dashboard, reports, and config API services
 * while preserving all existing behavior patterns and response formats.
 * Enhanced with environment configuration support.
 */
class UnifiedApiService {
  constructor() {
    // Initialize environment configuration if available
    this.envConfig = this.loadEnvironmentConfig();

    // Initialize configuration based on module context
    this.moduleContext = this.detectModuleContext();
    this.initializeConfiguration();

    // Credentials management
    this.credentials = { uid: '', password: '' };
    this.loadCredentials();

    // CSRF token manager for shared token handling
    if (typeof window !== 'undefined') {
      this.csrfTokenManager = window.csrfTokenManager;
    } else {
      try {
        this.csrfTokenManager = require('../security/csrf-token-manager');
      } catch (e) {
        this.csrfTokenManager = null;
      }
    }

    console.log(`Unified API Service initialized for ${this.moduleContext} module with environment: ${this.getCurrentEnvironment()}`);
  }

  /**
   * Load environment configuration if available
   */
  loadEnvironmentConfig() {
    try {
      // Try to load environment config if available
      if (typeof window !== 'undefined' && window.envConfig) {
        return window.envConfig;
      }
      return null;
    } catch (error) {
      console.warn('Environment configuration not available, using defaults:', error);
      return null;
    }
  }

  /**
   * Get current environment name
   */
  getCurrentEnvironment() {
    if (this.envConfig) {
      return this.envConfig.current.name;
    }
    return 'Default (QA02)';
  }

  /**
   * Detect which module is using this service based on current URL
   */
  detectModuleContext() {
    if (typeof window !== 'undefined') {
      const path = window.location.pathname;
      if (path.includes('/dashboard')) return 'dashboard';
      if (path.includes('/reports')) return 'reports';
      if (path.includes('/config')) return 'config';
    }
    return 'dashboard'; // default
  }

  /**
   * Initialize configuration based on module context and environment
   */
  initializeConfiguration() {
    // Dynamically determine the base URL from the current window location
    const origin = typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000';

    // Base URL configuration per module with environment support
    if (this.envConfig) {
      // For config module, always use local origin to avoid CORS issues
      // The local server will proxy requests to the external API
      this.baseUrls = {
        dashboard: this.envConfig.current.apiBaseUrl,
        reports: this.envConfig.current.apiBaseUrl,
        config: origin  // Always use local origin for config module
      };

      console.log(`Using environment-based URLs: dashboard/reports=${this.envConfig.current.apiBaseUrl}, config=${origin}`);
    } else {
      // Use the current origin for all modules
      this.baseUrls = {
        dashboard: origin,           // Use current origin
        reports: origin,            // Use current origin
        config: origin              // Use current origin
      };

      console.log(`Using dynamic base URL: ${origin}`);
    }

    // Content-Type configuration per module
    this.contentTypes = {
      dashboard: 'application/json',
      reports: 'application/json',
      config: 'application/x-www-form-urlencoded'
    };

    // Endpoints (unified from all three services)
    this.endpoints = {
      // Common endpoints
      caseRunner: '/api/case-runner',
      suiteRunner: '/api/suite-runner',
      testStatus: '/api/test-status',
      testReport: '/api/test-report',
      testReports: '/api/test-reports', // Fixed: added /api prefix to match server route
      testSuites: '/local/test-suites',
      testCases: '/local/test-cases',
      stopTest: '/api/stop-test',
      rerunFailed: '/api/rerun-failed',
      activeTests: '/local/active-tests',
      recentRuns: '/local/recent-runs',

      // Reports-specific endpoints
      testDetailsEndpoint: '/local/test-details',

      // Config-specific endpoints (uses different endpoint for run-suite)
      runSuite: '/run-suite'
    };

    // Default test parameters with environment support
    const envName = this.envConfig ? this.envConfig.currentEnv : 'qa02';
    this.defaultTestParams = {
      environment: envName,
      shell_host: 'jps-qa10-app01',
      file_path: '/home/<USER>/',
      operatorConfigs: 'operatorNameConfigs',
      kafka_server: 'kafka-qa-a0.lab.wagerworks.com',
      dataCenter: 'GU',
      rgs_env: envName,
      old_version: '0',
      networkType1: 'multi-site',
      networkType2: 'multi-site',
      sign: '-',
      rate_src: 'local'
    };
  }

  /**
   * Get base URL for current module context
   */
  getBaseUrl() {
    return this.baseUrls[this.moduleContext];
  }

  /**
   * Get content type for current module context
   */
  getContentType() {
    return this.contentTypes[this.moduleContext];
  }

  /**
   * Set API credentials (using shared credential manager)
   */
  setCredentials(username, password) {
    // Use shared credential manager for consistent storage logic
    if (window.credentialManager) {
      const result = window.credentialManager.setCredentials(username, password);
      this.credentials = window.credentialManager.getCredentials();
      return result;
    } else {
      // Fallback to legacy logic if credential manager not available
      console.warn('Shared credential manager not available, using fallback logic');
      this.credentials = { uid: username, password: password };

      try {
        sessionStorage.setItem('smarttest_uid', username);
        sessionStorage.setItem('smarttest_pwd', password);
        console.log(`API credentials set for user: ${username} (fallback)`);
      } catch (error) {
        console.warn('Could not save credentials to session storage:', error);
      }

      return true;
    }
  }

  /**
   * Load credentials from storage (using shared credential manager)
   */
  loadCredentials() {
    // First, try to get credentials from unified auth client (JWT authentication)
    if (window.unifiedAuthClient && window.unifiedAuthClient.isAuthenticated) {
      const user = window.unifiedAuthClient.getCurrentUser();
      if (user && user.uid) {
        this.credentials = { uid: user.uid, password: '' }; // JWT doesn't need password
        console.log(`Credentials loaded from unified auth client: ${user.uid}`);
        return true;
      }
    }

    // Check if credential manager is available, with retry logic for timing issues
    if (this._waitForCredentialManager()) {
      const result = window.credentialManager.loadCredentials();
      this.credentials = window.credentialManager.getCredentials();
      return result;
    } else {
      // Fallback to legacy logic if credential manager not available
      console.warn('Shared credential manager not available, using fallback logic');

      try {
        const uid = sessionStorage.getItem('smarttest_uid');
        const password = sessionStorage.getItem('smarttest_pwd');

        if (uid) { // Allow empty password for JWT authentication
          this.credentials = { uid, password: password || '' };
          console.log(`Credentials loaded for user: ${uid} (fallback)`);
          return true;
        }

        console.log('No valid credentials found, user needs to log in (fallback)');
        this.credentials = { uid: '', password: '' };
        return false;
      } catch (error) {
        console.error('Error loading credentials (fallback):', error);
        this.credentials = { uid: '', password: '' };
        return false;
      }
    }
  }

  /**
   * Wait for credential manager to be available (handles timing issues)
   * @private
   */
  _waitForCredentialManager() {
    // If already available, return immediately
    if (window.credentialManager) {
      return true;
    }

    // Try to wait a short time for the credential manager to load
    let attempts = 0;
    const maxAttempts = 10;
    const waitTime = 10; // milliseconds

    while (attempts < maxAttempts && !window.credentialManager) {
      // Synchronous wait (not ideal but necessary for initialization)
      const start = Date.now();
      while (Date.now() - start < waitTime) {
        // Busy wait
      }
      attempts++;
    }

    return !!window.credentialManager;
  }

  /**
   * Clear stored credentials (using shared credential manager)
   */
  clearCredentials() {
    if (window.credentialManager) {
      window.credentialManager.clearCredentials();
      this.credentials = window.credentialManager.getCredentials();
    } else {
      // Fallback to legacy logic
      this.credentials = { uid: '', password: '' };
      try {
        sessionStorage.removeItem('smarttest_uid');
        sessionStorage.removeItem('smarttest_pwd');
      } catch (error) {
        console.warn('Could not clear credentials from session storage:', error);
      }
    }
  }

  /**
   * Get authentication parameters (deprecated - now using JWT tokens)
   */
  getAuthParams() {
    // JWT authentication is now handled via cookies automatically
    // No need to send username/password parameters
    return {};
  }

  /**
   * Get CSRF token for state-changing operations
   */
  async getCSRFToken(forceRefresh = false) {
    if (!this.csrfTokenManager) return null;
    if (forceRefresh) {
      await this.csrfTokenManager.handleTokenExpiry();
    }
    return await this.csrfTokenManager.getToken();
  }

  /**
   * Make a GET request (preserving all existing behavior patterns)
   */
  async getRequest(endpoint, params = {}) {
    try {
      // Determine URL construction based on module context and endpoint type
      let url;
      const isLocalEndpoint = endpoint.startsWith('/local/');
      const baseUrl = this.getBaseUrl();
      
      // Always use the current origin for all endpoints
      // This ensures consistent URL construction across the application
      url = baseUrl + (endpoint.startsWith('/') ? endpoint : '/' + endpoint);
      
      console.log(`Constructing URL: Base=${baseUrl}, Endpoint=${endpoint}, Full URL=${url}`);
      
      // Ensure we're not doubling up on slashes
      url = url.replace(/([^:]\/)\/+/g, '$1');

      console.log(`Making GET request to: ${url}`);

      // Add authentication parameters
      const allParams = {
        ...params,
        ...this.getAuthParams()
      };

      // Build query string
      const queryString = Object.entries(allParams)
        .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
        .join('&');

      // Make the request with credentials (cookies) for JWT authentication
      const response = await fetch(`${url}?${queryString}`, {
        method: 'GET',
        credentials: 'include', // Include cookies for JWT tokens
        headers: {
          'Content-Type': 'application/json'
        }
      });

      // Handle non-200 responses
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`API error (${response.status}): ${errorText}`);
        throw new Error(`API request failed with status ${response.status}`);
      }

      // Parse the response
      return await response.json();
    } catch (error) {
      console.error(`Error making GET request to ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Make a POST request (preserving module-specific behavior)
   */
  async postRequest(endpoint, params = {}) {
    try {
      const url = this.getBaseUrl() + (endpoint.startsWith('/') ? endpoint : '/' + endpoint);
      console.log(`Making POST request to: ${url}`);

      // Add authentication parameters
      const requestData = {
        ...params,
        ...this.getAuthParams()
      };

      // Build request options based on module context
      const options = {
        method: 'POST',
        credentials: 'include', // Include cookies for JWT tokens
        headers: {
          'Content-Type': this.getContentType()
        }
      };

      // Add CSRF token for state-changing operations via manager
      if (this.csrfTokenManager) {
        await this.csrfTokenManager.includeInRequest(options);
      }

      // Handle body encoding based on module context and endpoint
      const isTestRunnerEndpoint = endpoint.includes('case-runner') || endpoint.includes('suite-runner');

      if (this.moduleContext === 'config' || (this.moduleContext === 'reports' && isTestRunnerEndpoint)) {
        // Config module and Reports test runner endpoints: use URLSearchParams for form encoding
        const formData = new URLSearchParams();
        Object.entries(requestData).forEach(([key, value]) => {
          formData.append(key, value);
        });
        options.body = formData;

        // Override content type for form encoding
        if (this.moduleContext === 'reports' && isTestRunnerEndpoint) {
          options.headers['Content-Type'] = 'application/x-www-form-urlencoded';
          console.log('🔄 Reports: Using form encoding for test runner endpoint');
        }
      } else {
        // Dashboard/Reports modules (non-test-runner): use JSON encoding
        options.body = JSON.stringify(requestData);
      }

      // Log request parameters for debugging (mask password)
      const logParams = {...requestData};
      if (logParams.password) logParams.password = '***';
      console.log('Request parameters:', logParams);

      // Make the request
      let response = await fetch(url, options);

      // Handle CSRF token validation failure with retry
      let errorText = null; // Declare errorText in broader scope

      if (response.status === 403) {
        let shouldRetry = false;

        try {
          // Clone the response to avoid "body stream already read" error
          const responseClone = response.clone();
          const errorData = await responseClone.json();

          if (errorData.code === 'CSRF_INVALID' || errorData.code === 'CSRF_SESSION_MISMATCH') {
            console.log(`🔄 CSRF error detected: ${errorData.code} - ${errorData.message}`);

            if (errorData.details?.reason) {
              console.log(`🔍 Failure reason: ${errorData.details.reason}`);
            }

            // Get fresh CSRF token and retry
            if (this.csrfTokenManager) {
              await this.csrfTokenManager.handleTokenExpiry();
              await this.csrfTokenManager.includeInRequest(options);

              console.log('🔄 Retrying request with fresh CSRF token...');
              response = await fetch(url, options);
              shouldRetry = true;

              // If retry also fails, log additional info
              if (!response.ok && response.status === 403) {
                console.warn('🚨 CSRF retry also failed - this may indicate a deeper session issue');
              }
            } else {
              console.error('❌ Failed to get fresh CSRF token for retry');
            }
          }
        } catch (parseError) {
          // If we can't parse the error as JSON, get the text for later use
          console.warn('Could not parse CSRF error response as JSON:', parseError);
          try {
            errorText = await response.text();
          } catch (textError) {
            console.warn('Could not read response as text either:', textError);
            errorText = 'Unknown error';
          }
        }
      }

      // Handle non-200 responses
      if (!response.ok) {
        let finalErrorText = errorText; // Use errorText from CSRF handling if available

        if (!finalErrorText) {
          try {
            finalErrorText = await response.text();
          } catch (readError) {
            console.warn('Could not read error response:', readError);
            finalErrorText = `HTTP ${response.status} error`;
          }
        }

        console.error(`API error (${response.status}): ${finalErrorText}`);

        // Try to parse error as JSON if possible (dashboard/reports behavior)
        if (this.moduleContext !== 'config') {
          try {
            const errorJson = JSON.parse(finalErrorText);
            if (errorJson && errorJson.message) {
              throw new Error(errorJson.message);
            }
          } catch (parseError) {
            // If can't parse JSON, just use the text
          }
        }

        throw new Error(`API request failed with status ${response.status}: ${finalErrorText}`);
      }

      // Parse JSON response
      const data = await response.json();

      // Add success property for config module compatibility
      if (this.moduleContext === 'config') {
        return { success: true, ...data };
      }

      return data;
    } catch (error) {
      console.error(`Error making POST request to ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Run a specific test case by ID (unified from all services)
   */
  async runTestCase(tcId, params = {}) {
    try {
      console.log('[DEBUG] UnifiedApiService.runTestCase: Received tcId:', tcId, 'tcId type:', typeof tcId);
      console.log('[DEBUG] UnifiedApiService.runTestCase: Received params (payload from frontend):', JSON.stringify(params, null, 2));

      // Ensure credentials are loaded before test execution
      if (!this.credentials.uid) {
        console.log('🔄 Refreshing credentials before test execution...');
        this.loadCredentials();
      }

      // Convert tcId to a string or number if it's an object
      if (typeof tcId === 'object') {
        console.error('[CRITICAL] tcId is an object in UnifiedApiService! Converting to string.');
        tcId = String(tcId);
      }
      // Build test parameters based on module context
      let testParams;

      if (this.moduleContext === 'dashboard') {
        // Dashboard module behavior
        testParams = {
          tc_id: tcId,
          user_id: this.credentials.uid,
          username: this.credentials.uid,
          ...this.defaultTestParams,
          ...params
        };
      } else if (this.moduleContext === 'config') {
        // Config module behavior - supports all configuration parameters
        // Include user credentials for backend authentication
        testParams = {
          tc_id: tcId,
          uid: this.credentials.uid,
          envir: params.envir || 'qa02',
          shell_host: params.shell_host || 'jps-qa10-app01',
          // Spread all additional parameters (including new configuration overrides)
          ...params
        };

        // Log configuration overrides for debugging
        const configOverrides = Object.keys(params).filter(key =>
          !['envir', 'shell_host'].includes(key)
        );
        if (configOverrides.length > 0) {
          console.log(`[UnifiedApiService] Configuration overrides provided:`, configOverrides);
        }
      } else {
        // Reports module behavior
        testParams = {
          tc_id: tcId,
          ...this.defaultTestParams,
          ...params
        };
      }

      console.log(`Running test case ${tcId} with params:`, testParams);
      console.log('[DEBUG] UnifiedApiService.runTestCase: Final testParams before API call:', JSON.stringify(testParams, null, 2));

      const response = await this.postRequest(this.endpoints.caseRunner, testParams);

      // Handle response based on module context
      if (this.moduleContext === 'dashboard') {
        if (response.success) {
          console.log(`Test case ${tcId} running with session ID: ${response.tsn_id}`);
          return response;
        } else {
          throw new Error(response.message || `Failed to run test case ${tcId}`);
        }
      } else if (this.moduleContext === 'config') {
        console.log(`Test case ${tcId} run initiated:`, response);
        return response;
      } else {
        // Reports module
        if (response && response.tsn_id) {
          return response;
        } else {
          throw new Error('Failed to get test suite run ID');
        }
      }
    } catch (error) {
      console.error(`Error running test case ${tcId}:`, error);
      throw error;
    }
  }

  /**
   * Run a test suite (unified from all services)
   */
  async runTestSuite(tsId, params = {}) {
    try {
      if (!tsId) {
        throw new Error('Test suite ID is required');
      }

      // Build test parameters based on module context
      let testParams;
      let endpoint;

      if (this.moduleContext === 'config') {
        // Config module uses different endpoint and parameters
        testParams = {
          ts_id: tsId,
          user_id: this.credentials.uid,
          username: this.credentials.uid,
          ...this.defaultTestParams,
          ...params
        };
        endpoint = this.endpoints.runSuite; // '/run-suite'
      } else {
        // Dashboard and Reports modules use caseRunner endpoint
        testParams = {
          ts_id: tsId,
          user_id: this.credentials.uid,
          username: this.credentials.uid,
          ...this.defaultTestParams,
          ...params
        };
        endpoint = this.endpoints.suiteRunner; // '/suite-runner'
      }

      console.log(`Running test suite ${tsId} with params:`, testParams);

      const response = await this.postRequest(endpoint, testParams);

      if (response && response.tsn_id) {
        console.log(`Test suite ${tsId} running with session ID: ${response.tsn_id}`);
        return response.tsn_id;
      } else {
        throw new Error(response.message || `Failed to run test suite ${tsId}`);
      }
    } catch (error) {
      console.error(`Error running test suite ${tsId}:`, error);
      throw error;
    }
  }

  /**
   * Get test status (unified from all services)
   */
  async getTestStatus(tsnId) {
    try {
      return await this.getRequest(this.endpoints.testStatus, { tsn_id: tsnId });
    } catch (error) {
      console.error(`Error getting test status for ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get report summary (unified from all services)
   */
  async getReportSummary(tsnId) {
    try {
      if (this.moduleContext === 'dashboard') {
        // Dashboard uses REST-style URL pattern
        return await this.getRequest(`${this.endpoints.testReports}/${tsnId}/summary`);
      } else {
        // Reports and Config use query parameter pattern
        return await this.getRequest(this.endpoints.testReport, { tsn_id: tsnId });
      }
    } catch (error) {
      console.error(`Error getting report summary for ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get test report (unified from all services)
   */
  async getTestReport(tsnId) {
    try {
      if (this.moduleContext === 'dashboard') {
        // Dashboard uses REST-style URL pattern
        return await this.getRequest(`${this.endpoints.testReports}/${tsnId}`);
      } else {
        // Reports and Config use query parameter pattern
        return await this.getRequest(this.endpoints.testReport, { tsn_id: tsnId });
      }
    } catch (error) {
      console.error(`Error getting report for ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get available test suites (unified with response format handling)
   */
  async getTestSuites() {
    try {
      const response = await this.getRequest(this.endpoints.testSuites);

      // Handle different response formats based on module context
      if (this.moduleContext === 'dashboard') {
        return response.success && Array.isArray(response.data) ? response.data : [];
      } else {
        // Reports and Config modules
        return response.testSuites || [];
      }
    } catch (error) {
      console.error('Error getting test suites:', error);
      throw error;
    }
  }

  /**
   * Get available test cases (unified with response format handling)
   */
  async getTestCases() {
    try {
      const response = await this.getRequest(this.endpoints.testCases);

      // Handle different response formats based on module context
      if (this.moduleContext === 'dashboard') {
        return response.success && Array.isArray(response.data) ? response.data : [];
      } else if (this.moduleContext === 'config') {
        return response.success && Array.isArray(response.data) ? response.data : [];
      } else {
        // Reports module
        return response.testCases || [];
      }
    } catch (error) {
      console.error('Error getting test cases:', error);
      throw error;
    }
  }

  /**
   * Search test cases with criteria (config module specific)
   */
  async searchTestCases(criteria = {}) {
    try {
      console.log('UnifiedApiService.searchTestCases called with criteria:', criteria);
      const response = await this.getRequest(this.endpoints.testCases, criteria);

      // Handle different response formats based on module context
      if (this.moduleContext === 'dashboard') {
        return response.success && Array.isArray(response.data) ? response.data : [];
      } else if (this.moduleContext === 'config') {
        return response.success && Array.isArray(response.data) ? response.data : [];
      } else {
        // Reports module
        return response.testCases || [];
      }
    } catch (error) {
      console.error('Error searching test cases:', error);
      throw error;
    }
  }

  /**
   * Stop a running test (unified from all services)
   */
  async stopTest(tsnId) {
    try {
      const response = await this.postRequest(this.endpoints.stopTest, { tsn_id: tsnId });
      return response.success === true;
    } catch (error) {
      console.error(`Error stopping test ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Rerun failed tests (unified from all services)
   */
  async rerunFailedTests(tsnId, params = {}) {
    try {
      const response = await this.postRequest(this.endpoints.rerunFailed, {
        tsn_id: tsnId,
        user_id: this.credentials.uid,
        username: this.credentials.uid,
        ...params
      });

      if (response && response.tsn_id) {
        return response.tsn_id;
      } else {
        throw new Error('Failed to get test suite run ID for rerun');
      }
    } catch (error) {
      console.error(`Error rerunning failed tests from ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get active tests (unified with response format handling)
   */
  async getActiveTests() {
    try {
      const response = await this.getRequest(this.endpoints.activeTests);

      // Handle different response formats based on module context
      if (this.moduleContext === 'dashboard') {
        return response.success && Array.isArray(response.data) ? response.data : [];
      } else {
        // Reports and Config modules
        return response.activeTests || [];
      }
    } catch (error) {
      console.error('Error getting active tests:', error);
      throw error;
    }
  }

  /**
   * Get dashboard data (dashboard module specific)
   */
  async getDashboardData() {
    try {
      const activeTestsData = await this.getActiveTests();

      const recentRuns = activeTestsData.map(test => ({
        id: test.tsn_id,
        type: 'Test Case',
        environment: 'QA02',
        status: test.status || 'running',
        startTime: test.latest_activity || new Date().toISOString(),
        duration: 0
      }));

      const total = activeTestsData.length;
      const successful = activeTestsData.filter(test => test.outcome === 'P').length;
      const failed = activeTestsData.filter(test => test.outcome === 'F').length;
      const running = activeTestsData.filter(test => !test.outcome).length;

      return {
        summary: { total, successful, failed, running },
        recent: recentRuns,
        environment: 'QA02'
      };
    } catch (error) {
      console.error('Error getting dashboard data:', error);
      throw error;
    }
  }

  /**
   * Get test reports (unified with response format handling)
   */
  async getTestReports(params = {}) {
    try {
      const response = await this.getRequest(this.endpoints.testReports, params);

      // Handle different response formats based on module context
      if (this.moduleContext === 'dashboard') {
        return response.success && Array.isArray(response.data) ? response.data : [];
      } else if (this.moduleContext === 'config') {
        return response.reports || [];
      } else {
        // Reports module - return full response object
        return response;
      }
    } catch (error) {
      console.error('Error getting test reports:', error);
      if (this.moduleContext === 'reports') {
        // Reports module expects structured error response
        return { success: false, message: error.message || 'Network or API error' };
      }
      throw error;
    }
  }

  /**
   * Get recent runs (reports module specific)
   */
  async getRecentRuns(options = {}) {
    try {
      const response = await this.getRequest(this.endpoints.recentRuns, options);

      // Handle both response formats for backward compatibility
      if (response.success && Array.isArray(response.data)) {
        return response.data;
      } else if (Array.isArray(response)) {
        return response;
      }
      return [];
    } catch (error) {
      console.error('Error getting recent runs:', error);
      throw error;
    }
  }

  /**
   * Get test details (reports module specific)
   * @param {string} tsnId - Test session ID
   * @returns {Promise<Object>} - Test details with test cases
   */
  async getTestDetails(tsnId) {
    try {
      console.log(`Getting test details for ${tsnId} using endpoint ${this.endpoints.testDetailsEndpoint}`);

      const response = await this.getRequest(`${this.endpoints.testDetailsEndpoint}/${tsnId}`);
      console.log(`[DEBUG] getTestDetails: Raw API response:`, response);

      let result;
      // Handle different response formats - our standardized endpoint now returns both test and data properties
      if (response.success && response.test) {
        console.log(`[DEBUG] getTestDetails: Found test in response.test`);
        result = response.test;
      } else if (response.success && response.data) {
        console.log(`[DEBUG] getTestDetails: Found test in response.data`);
        result = response.data;
      } else if (response.test) {
        console.log(`[DEBUG] getTestDetails: Found direct test property`);
        result = response.test;
      } else {
        console.log(`[DEBUG] getTestDetails: Using full response as result`);
        result = response;
      }

      // Validate and log details about test cases in the result
      if (result) {
        console.log(`[DEBUG] getTestDetails: Test cases in result: ${result.test_cases ? result.test_cases.length : 'none'}`);
        if (result.test_cases && result.test_cases.length > 0) {
          console.log(`[DEBUG] getTestDetails: First test case:`, result.test_cases[0]);
        } else {
          console.warn(`[DEBUG] getTestDetails: No test cases found in the result for ${tsnId}`);
        }
      } else {
        console.error(`[DEBUG] getTestDetails: No valid result data found for ${tsnId}`);
      }

      return result;
    } catch (error) {
      console.error(`Error getting test details for ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get default test parameters
   */
  static get DEFAULT_TEST_PARAMS() {
    return {
      environment: 'qa02',
      shell_host: 'jps-qa10-app01',
      file_path: '/home/<USER>/',
      operatorConfigs: 'operatorNameConfigs',
      kafka_server: 'kafka-qa-a0.lab.wagerworks.com',
      dataCenter: 'GU',
      rgs_env: 'qa02',
      old_version: '0',
      networkType1: 'multi-site',
      networkType2: 'multi-site',
      sign: '-',
      rate_src: 'local'
    };
  }
}

// CRITICAL: Immediately make the class and instance globally available
// This must happen synchronously for other scripts to find it
if (typeof window !== 'undefined') {
  // Make the class available globally
  window.UnifiedApiService = UnifiedApiService;
  
  // Create a global singleton instance - THIS IS THE PRIMARY API SERVICE INSTANCE
  window.apiService = new UnifiedApiService();
  
  // For backward compatibility
  window.unifiedApiService = window.apiService;
  
  console.log('UnifiedApiService class and global apiService instance initialized');
  
  // Dispatch a custom event to notify all scripts that API service is ready
  // This helps with timing issues and race conditions
  setTimeout(() => {
    document.dispatchEvent(new CustomEvent('apiservice-ready', {
      detail: { apiService: window.apiService }
    }));
    console.log('Dispatched apiservice-ready event');
  }, 100);
}

// Make UnifiedApiService class available globally for non-module scripts (Dashboard/Reports)
// This is essential since Dashboard loads this file as a regular script
window.UnifiedApiService = UnifiedApiService;

// Export for CommonJS (Node.js) environments if module is defined
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { 
    UnifiedApiService: UnifiedApiService,
    unifiedApiService: window.unifiedApiService 
  };
}

// DO NOT use ES6 export syntax here - it will break Dashboard/Reports
// ES6 export statements are syntax errors in regular script contexts

// For module compatibility, we use a technique that doesn't involve ES6 exports
// Custom Test Runner now uses window.UnifiedApiService directly

// Console output for debugging
console.log('Unified API Service loaded with global compatibility mode');


