/**
 * Unified Authentication Client
 * Provides centralized authentication management for all frontend modules
 */

class UnifiedAuthClient {
  constructor() {
    this.isAuthenticated = false;
    this.currentUser = null;
    this.permissions = [];
    this.sessionId = null;
    this.sessionExpiry = null;
    this.refreshTimer = null;
    this.validationInterval = null;
    this.csrfToken = null;
    if (typeof window !== 'undefined') {
      this.csrfTokenManager = window.csrfTokenManager;
    } else {
      try {
        this.csrfTokenManager = require('../security/csrf-token-manager');
      } catch (e) {
        this.csrfTokenManager = null;
      }
    }

    // Event listeners for auth state changes
    this.listeners = {
      login: [],
      logout: [],
      sessionExpired: [],
      permissionDenied: []
    };

    // Initialize from stored session
    this.initializeFromStorage();
  }

  /**
   * Initialize authentication state from storage
   */
  initializeFromStorage() {
    try {
      const stored = localStorage.getItem('smarttest_auth');
      if (stored) {
        const data = JSON.parse(stored);
        console.log('🔍 Stored session data:', JSON.stringify(data, null, 2));
        const { uid, sessionId, sessionExpiry, user, permissions, accessToken } = data;

        // Fix: Handle both uid at top level and user.uid
        const actualUid = uid || user?.uid;

        // Fix: sessionExpiry should be compared as timestamp, ensure it's a number
        let expiryTime;
        try {
          console.log(`🔍 Raw sessionExpiry from storage: ${sessionExpiry} (type: ${typeof sessionExpiry})`);
          expiryTime = typeof sessionExpiry === 'string' ? new Date(sessionExpiry).getTime() : sessionExpiry;
          console.log(`🔍 Converted expiryTime: ${expiryTime} (type: ${typeof expiryTime})`);

          // Validate that expiryTime is a valid number
          if (isNaN(expiryTime) || expiryTime <= 0) {
            console.warn(`⚠️ Invalid session expiry time: ${expiryTime}, clearing stored auth`);
            console.warn(`⚠️ Original sessionExpiry: ${sessionExpiry} (type: ${typeof sessionExpiry})`);
            this.clearStoredAuth();
            return false;
          }
        } catch (timeError) {
          console.warn('⚠️ Error parsing session expiry time:', timeError);
          console.warn(`⚠️ Original sessionExpiry: ${sessionExpiry} (type: ${typeof sessionExpiry})`);
          this.clearStoredAuth();
          return false;
        }

        const currentTime = Date.now();
        console.log(`🔍 Session check: uid=${!!uid}, sessionId=${!!sessionId}, expiryTime=${expiryTime}, user=${!!user}`);
        console.log(`🔍 Extracted values: uid="${uid}", sessionId="${sessionId}", user.uid="${user?.uid}", actualUid="${actualUid}"`);

        try {
          console.log(`🔍 Time comparison: current=${currentTime} (${new Date(currentTime).toISOString()}), expiry=${expiryTime} (${new Date(expiryTime).toISOString()}), valid=${currentTime < expiryTime}`);
        } catch (isoError) {
          console.warn('⚠️ Error converting time to ISO string:', isoError);
          console.log(`🔍 Time comparison: current=${currentTime}, expiry=${expiryTime}, valid=${currentTime < expiryTime}`);
        }
        if (actualUid && sessionId && expiryTime && user && currentTime < expiryTime) {
          this.isAuthenticated = true;
          this.currentUser = user;
          this.permissions = permissions || [];
          this.sessionId = sessionId;
          this.sessionExpiry = expiryTime;
          this.accessToken = accessToken;
          console.log('✅ Restored JWT authentication session for:', actualUid);
          this.setupSessionRefresh();
          this.notifyListeners('login', { user: this.currentUser, permissions: this.permissions });
          if (window.authEventBus) {
            window.authEventBus.dispatch('login', data);
          }
          return true;
        } else {
          try {
            console.log(`⏰ Stored JWT session has expired or invalid. Current time: ${new Date(currentTime).toISOString()}, Expiry: ${new Date(expiryTime).toISOString()}`);
          } catch (isoError) {
            console.log(`⏰ Stored JWT session has expired or invalid. Current time: ${currentTime}, Expiry: ${expiryTime}`);
          }
          console.log(`🔍 Validation failed: uid=${!!uid}, actualUid=${!!actualUid}, sessionId=${!!sessionId}, expiryTime=${!!expiryTime}, user=${!!user}, timeValid=${currentTime < expiryTime}`);
          this.clearStoredAuth();
        }
      }
    } catch (error) {
      console.error('Error initializing JWT auth from storage:', error);
      this.clearStoredAuth();
    }
    return false;
  }

  /**
   * Login with credentials
   * @param {string} uid - User ID (email)
   * @param {string} password - Password
   * @returns {Promise<Object>} Login result
   */
  async login(uid, password) {
    try {
      // Clear any existing auth state
      this.logout(false);

      // Make authentication request to new auth endpoint
      const response = await fetch('/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include', // Include cookies
        body: JSON.stringify({ uid, password })
      });

      if (response.ok) {
        const data = await response.json();
        console.log('🔍 Server login response:', JSON.stringify(data, null, 2));

        // Store authentication state
        this.isAuthenticated = true;
        this.currentUser = data.user;
        this.permissions = data.permissions;
        this.sessionId = data.sessionId || this.generateSessionId(); // Fix: Ensure sessionId is set
        this.accessToken = data.accessToken; // Fix: Store access token
        this.sessionExpiry = Date.now() + (data.expiresIn * 1000);
        console.log(`🔍 JWT Session expiry set: ${new Date(this.sessionExpiry).toISOString()}, expiresIn: ${data.expiresIn}s`);
        console.log(`🔍 Session ID set: ${this.sessionId}`);
        console.log(`🔍 Current user after login:`, JSON.stringify(this.currentUser, null, 2));

        // Store authentication state
        this.storeAuthState();

        // Set up session refresh
        this.setupSessionRefresh();

        // Start periodic session validation
        this.startPeriodicValidation();

        console.log('✅ Login successful for:', uid);

        // Notify listeners
        this.notifyListeners('login', { user: this.currentUser, permissions: this.permissions });
        if (window.authEventBus) {
          window.authEventBus.dispatch('login', { user: this.currentUser, sessionId: this.sessionId, sessionExpiry: this.sessionExpiry, permissions: this.permissions });
        }
        return { success: true, user: this.currentUser };
      } else {
        const error = await response.json();
        console.log('❌ Login failed:', error.message);

        return {
          success: false,
          error: error.message || 'Authentication failed',
          code: error.code,
          retryAfter: error.retryAfter
        };
      }
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        error: 'Connection error: ' + error.message
      };
    }
  }

  /**
   * Logout user
   * @param {boolean} notifyServer - Whether to notify server of logout
   */
  async logout(notifyServer = true) {
    try {
      if (notifyServer) {
        // Notify server of logout using new auth endpoint
        await fetch('/auth/logout', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include' // Include cookies
        }).catch(err => console.warn('Logout notification failed:', err));
      }
    } catch (error) {
      console.warn('Error during logout notification:', error);
    }

    // Clear local state
    this.isAuthenticated = false;
    this.currentUser = null;
    this.permissions = [];
    this.sessionId = null;
    this.sessionExpiry = null;
    this.csrfToken = null;
    if (this.csrfTokenManager) {
      await this.csrfTokenManager.handleTokenExpiry();
    }

    // Clear refresh timer
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }

    // Stop periodic validation
    this.stopPeriodicValidation();

    // Clear storage
    this.clearStoredAuth();

    console.log('✅ Logout completed');

    // Notify listeners
    this.notifyListeners('logout');
    if (window.authEventBus) {
      window.authEventBus.dispatch('logout');
    }
  }

  /**
   * Force logout - clears everything without server notification
   * Useful for handling session expiration or security issues
   */
  forceLogout() {
    console.log('🚨 Force logout initiated');

    // Clear local state immediately
    this.isAuthenticated = false;
    this.currentUser = null;
    this.permissions = [];
    this.sessionId = null;
    this.sessionExpiry = null;
    this.csrfToken = null;
    if (this.csrfTokenManager) {
      this.csrfTokenManager.handleTokenExpiry();
    }

    // Clear all timers
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
    this.stopPeriodicValidation();

    // Clear all storage
    this.clearStoredAuth();

    // Notify listeners
    this.notifyListeners('logout');
    if (window.authEventBus) {
      window.authEventBus.dispatch('logout');
    }

    console.log('✅ Force logout completed');
  }

  /**
   * Logout from all sessions (admin feature)
   * @param {string} uid - User ID to logout from all sessions
   * @returns {Promise<boolean>} Success status
   */
  async logoutAllSessions(uid = null) {
    try {
      const targetUid = uid || (this.currentUser ? this.currentUser.uid : null);

      if (!targetUid) {
        console.warn('No user ID provided for logout all sessions');
        return false;
      }

      const response = await fetch('/auth/logout-all', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({ uid: targetUid })
      });

      if (response.ok) {
        console.log(`✅ All sessions logged out for user: ${targetUid}`);

        // If logging out current user, clear local state
        if (!uid || uid === this.currentUser?.uid) {
          this.forceLogout();
        }

        return true;
      } else {
        console.log('❌ Logout all sessions failed');
        return false;
      }
    } catch (error) {
      console.error('Logout all sessions error:', error);
      return false;
    }
  }

  /**
   * Check if user has specific permission
   * @param {string} permission - Permission to check
   * @returns {boolean} True if user has permission
   */
  hasPermission(permission) {
    return this.isAuthenticated && this.permissions.includes(permission);
  }

  /**
   * Check if user has specific role
   * @param {string|Array} roles - Role(s) to check
   * @returns {boolean} True if user has role
   */
  hasRole(roles) {
    if (!this.isAuthenticated || !this.currentUser) {
      return false;
    }

    const requiredRoles = Array.isArray(roles) ? roles : [roles];
    return requiredRoles.includes(this.currentUser.role);
  }

  /**
   * Get current user info
   * @returns {Object|null} Current user object
   */
  getCurrentUser() {
    return this.currentUser;
  }

  /**
   * Get user permissions
   * @returns {Array} Array of permissions
   */
  getPermissions() {
    return [...this.permissions];
  }

  /**
   * Check if session is valid
   * @returns {boolean} True if session is valid
   */
  isSessionValid() {
    return this.isAuthenticated &&
           this.sessionExpiry &&
           Date.now() < this.sessionExpiry;
  }

  /**
   * Validate session with server
   * @returns {Promise<boolean>} True if session is valid on server
   */
  async validateSessionWithServer() {
    try {
      const response = await fetch('/auth/validate', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include' // Include cookies
      });

      if (response.ok) {
        const data = await response.json();

        // Update local session data
        this.currentUser = data.user;
        this.permissions = data.permissions;

        // Fix: Ensure consistent session expiry format (always use timestamp)
        if (data.expiresAt) {
          // If expiresAt is provided, convert to timestamp if needed
          this.sessionExpiry = typeof data.expiresAt === 'string' ? new Date(data.expiresAt).getTime() : data.expiresAt;
        } else if (data.expiresIn) {
          // If expiresIn is provided, calculate timestamp
          this.sessionExpiry = Date.now() + (data.expiresIn * 1000);
        }

        console.log(`🔍 Session validation - updated expiry: ${this.sessionExpiry} (${new Date(this.sessionExpiry).toISOString()})`);
        this.storeAuthState();

        console.log('✅ Session validated with server');
        return true;
      } else {
        console.log('❌ Server session validation failed');
        this.handleSessionExpired();
        return false;
      }
    } catch (error) {
      console.error('Session validation error:', error);
      return false;
    }
  }

  /**
   * Start periodic session validation
   * @param {number} intervalMs - Validation interval in milliseconds (default: 5 minutes)
   */
  startPeriodicValidation(intervalMs = 5 * 60 * 1000) {
    if (this.validationInterval) {
      clearInterval(this.validationInterval);
    }

    this.validationInterval = setInterval(async () => {
      if (this.isAuthenticated) {
        console.log('🔍 Performing periodic session validation...');
        await this.validateSessionWithServer();
      }
    }, intervalMs);

    console.log(`⏰ Periodic session validation started (every ${intervalMs / 1000} seconds)`);
  }

  /**
   * Stop periodic session validation
   */
  stopPeriodicValidation() {
    if (this.validationInterval) {
      clearInterval(this.validationInterval);
      this.validationInterval = null;
      console.log('⏹️ Periodic session validation stopped');
    }
  }

  /**
   * Refresh session using refresh token
   * @returns {Promise<boolean>} True if refresh successful
   */
  async refreshSession() {
    try {
      const response = await fetch('/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include' // Include cookies with refresh token
      });

      if (response.ok) {
        const data = await response.json();

        // Update session expiry
        this.sessionExpiry = Date.now() + (data.expiresIn * 1000);
        this.storeAuthState();
        if (window.authEventBus) {
          window.authEventBus.dispatch('token-refresh', { sessionId: this.sessionId, sessionExpiry: this.sessionExpiry });
        }

        console.log('✅ Session refreshed');
        return true;
      } else {
        console.log('❌ Session refresh failed');
        this.handleSessionExpired();
        return false;
      }
    } catch (error) {
      console.error('Session refresh failed:', error);
      this.handleSessionExpired();
      return false;
    }
  }

  /**
   * Set up automatic session refresh
   */
  setupSessionRefresh() {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }

    if (!this.sessionExpiry) {
      return;
    }

    // Refresh 2 minutes before expiry (access tokens are short-lived)
    const refreshTime = this.sessionExpiry - Date.now() - (2 * 60 * 1000);

    if (refreshTime > 0) {
      this.refreshTimer = setTimeout(async () => {
        console.log('🔄 Attempting automatic token refresh...');
        const success = await this.refreshSession();

        if (success) {
          // Set up next refresh
          this.setupSessionRefresh();
        } else {
          console.log('❌ Automatic refresh failed, session will expire');
        }
      }, refreshTime);

      console.log(`⏰ Automatic refresh scheduled in ${Math.round(refreshTime / 1000)} seconds`);
    } else {
      console.log('⚠️ Session expires soon, attempting immediate refresh...');
      this.refreshSession().then(success => {
        if (success) {
          this.setupSessionRefresh();
        }
      });
    }
  }

  /**
   * Handle session expiration
   */
  handleSessionExpired() {
    console.log('⏰ Session expired');
    
    // Notify listeners before clearing state
    this.notifyListeners('sessionExpired');
    
    // Clear auth state
    this.logout(false);
  }

  /**
   * Store authentication state in session storage
   */
  storeAuthState() {
    try {
      if (this.currentUser) {
        console.log('🔍 Storing auth state - currentUser:', JSON.stringify(this.currentUser, null, 2));
        console.log('🔍 Storing auth state - currentUser.uid:', this.currentUser.uid);
        console.log('🔍 Storing auth state - sessionId:', this.sessionId);

        const data = {
          uid: this.currentUser.uid,
          sessionId: this.sessionId,
          sessionExpiry: this.sessionExpiry,
          user: this.currentUser,
          permissions: this.permissions,
          accessToken: this.accessToken
        };

        console.log('🔍 Data to be stored:', JSON.stringify(data, null, 2));
        console.log(`🔍 Session expiry being stored: ${this.sessionExpiry} (type: ${typeof this.sessionExpiry})`);
        console.log(`🔍 Session expiry as ISO: ${new Date(this.sessionExpiry).toISOString()}`);
        localStorage.setItem('smarttest_auth', JSON.stringify(data));
        console.log('✅ JWT authentication state stored for:', this.currentUser.uid);

        // Verify what was actually stored
        const stored = localStorage.getItem('smarttest_auth');
        console.log('🔍 Verification - what was actually stored:', stored);
      }
    } catch (error) {
      console.warn('Failed to store JWT auth state:', error);
    }
  }

  /**
   * Clear stored authentication data
   */
  clearStoredAuth() {
    try {
      // Clear session storage
      sessionStorage.removeItem('smarttest_uid');
      sessionStorage.removeItem('smarttest_pwd');
      sessionStorage.removeItem('smarttest_session_id');
      sessionStorage.removeItem('smarttest_session_expiry');
      sessionStorage.removeItem('smarttest_user');
      sessionStorage.removeItem('smarttest_permissions');

      // Clear any other auth-related storage
      localStorage.removeItem('userCredentials');
      localStorage.removeItem('smarttest_auth');

      // Clear any cached API service credentials
      if (window.apiService && typeof window.apiService.clearCredentials === 'function') {
        window.apiService.clearCredentials();
      }

      console.log('✅ All stored authentication data cleared');
    } catch (error) {
      console.warn('Failed to clear stored auth:', error);
    }
  }

  /**
   * Generate session ID
   * @returns {string} Session ID
   */
  generateSessionId() {
    return 'sess_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
  }

  /**
   * Get CSRF token
   * @returns {Promise<string>} CSRF token
   */
  async getCSRFToken(forceRefresh = false) {
    if (!this.csrfTokenManager) return null;
    if (forceRefresh) {
      await this.csrfTokenManager.handleTokenExpiry();
    }
    return await this.csrfTokenManager.getToken();
  }

  /**
   * Make authenticated request with CSRF protection
   * @param {string} url - Request URL
   * @param {Object} options - Fetch options
   * @returns {Promise<Response>} Fetch response
   */
  async makeAuthenticatedRequest(url, options = {}) {
    try {
      // Get CSRF token for state-changing operations
      const needsCSRF = options.method && ['POST', 'PUT', 'DELETE', 'PATCH'].includes(options.method.toUpperCase());

      if (needsCSRF && this.csrfTokenManager) {
        options = await this.csrfTokenManager.includeInRequest(options);
      }

      // Ensure credentials are included
      options.credentials = 'include';

      // Make the request
      const response = await fetch(url, options);

      // If CSRF token is invalid, try to get a new one and retry once
      if (response.status === 403 && needsCSRF) {
        const errorData = await response.json().catch(() => ({}));
        if (this.csrfTokenManager && (errorData.code === 'CSRF_INVALID' || errorData.code === 'CSRF_SESSION_MISMATCH')) {
          console.log('🔄 CSRF token invalid, getting new token and retrying...');
          await this.csrfTokenManager.handleTokenExpiry();
          await this.csrfTokenManager.includeInRequest(options);
          return fetch(url, options);
        }
      }

      return response;
    } catch (error) {
      console.error('Authenticated request error:', error);
      throw error;
    }
  }

  /**
   * Add event listener
   * @param {string} event - Event name (login, logout, sessionExpired, permissionDenied)
   * @param {Function} callback - Callback function
   */
  addEventListener(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event].push(callback);
    }
  }

  /**
   * Remove event listener
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   */
  removeEventListener(event, callback) {
    if (this.listeners[event]) {
      const index = this.listeners[event].indexOf(callback);
      if (index > -1) {
        this.listeners[event].splice(index, 1);
      }
    }
  }

  /**
   * Notify event listeners
   * @param {string} event - Event name
   * @param {Object} data - Event data
   */
  notifyListeners(event, data = {}) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in ${event} listener:`, error);
        }
      });
    }
  }
}

// Create and export singleton instance
const unifiedAuthClient = new UnifiedAuthClient();

// Make it globally available
if (typeof window !== 'undefined') {
  window.unifiedAuthClient = unifiedAuthClient;
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = unifiedAuthClient;
}
