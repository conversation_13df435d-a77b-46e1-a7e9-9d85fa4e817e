/**
 * Authentication Loader
 * Provides a unified way to load and initialize authentication across all modules
 */

(function(global) {
    'use strict';

    // Authentication loader configuration
    const AUTH_CONFIG = {
        // Path to unified auth client
        unifiedAuthPath: '/shared/auth/unified-auth-client.js',
        
        // Timeout for loading auth client
        loadTimeout: 5000,
        
        // Retry interval
        retryInterval: 100
    };

    /**
     * Authentication Loader Class
     */
    class AuthLoader {
        constructor() {
            this.isLoaded = false;
            this.isLoading = false;
            this.loadPromise = null;
            this.callbacks = [];
            this.errorCallbacks = [];
        }

        /**
         * Load unified authentication client
         * @returns {Promise} Promise that resolves when auth client is loaded
         */
        async load() {
            if (this.isLoaded && global.unifiedAuthClient) {
                return global.unifiedAuthClient;
            }

            if (this.isLoading) {
                return this.loadPromise;
            }

            this.isLoading = true;
            this.loadPromise = this._loadAuthClient();

            try {
                const authClient = await this.loadPromise;
                this.isLoaded = true;
                this.isLoading = false;
                
                // Notify all waiting callbacks
                this.callbacks.forEach(callback => {
                    try {
                        callback(authClient);
                    } catch (error) {
                        console.error('Error in auth load callback:', error);
                    }
                });
                this.callbacks = [];

                return authClient;
            } catch (error) {
                this.isLoading = false;
                this.loadPromise = null;
                
                // Notify error callbacks
                this.errorCallbacks.forEach(callback => {
                    try {
                        callback(error);
                    } catch (err) {
                        console.error('Error in auth error callback:', err);
                    }
                });
                
                throw error;
            }
        }

        /**
         * Internal method to load the auth client
         * @private
         */
        async _loadAuthClient() {
            // Check if already available
            if (global.unifiedAuthClient) {
                return global.unifiedAuthClient;
            }

            // Try to load the script
            try {
                await this._loadScript(AUTH_CONFIG.unifiedAuthPath);
                
                // Wait for the auth client to be available
                const authClient = await this._waitForAuthClient();
                return authClient;
            } catch (error) {
                console.error('Failed to load unified auth client:', error);
                throw new Error('Authentication system unavailable');
            }
        }

        /**
         * Load script dynamically
         * @param {string} src - Script source URL
         * @private
         */
        _loadScript(src) {
            return new Promise((resolve, reject) => {
                // Check if script is already loaded
                const existingScript = document.querySelector(`script[src="${src}"]`);
                if (existingScript) {
                    resolve();
                    return;
                }

                const script = document.createElement('script');
                script.src = src;
                script.async = true;
                
                script.onload = () => resolve();
                script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
                
                document.head.appendChild(script);
            });
        }

        /**
         * Wait for auth client to be available
         * @private
         */
        _waitForAuthClient() {
            return new Promise((resolve, reject) => {
                const startTime = Date.now();
                
                const checkInterval = setInterval(() => {
                    if (global.unifiedAuthClient) {
                        clearInterval(checkInterval);
                        resolve(global.unifiedAuthClient);
                        return;
                    }

                    // Check timeout
                    if (Date.now() - startTime > AUTH_CONFIG.loadTimeout) {
                        clearInterval(checkInterval);
                        reject(new Error('Timeout waiting for unified auth client'));
                    }
                }, AUTH_CONFIG.retryInterval);
            });
        }

        /**
         * Register callback for when auth client is loaded
         * @param {Function} callback - Callback function
         */
        onLoad(callback) {
            if (this.isLoaded && global.unifiedAuthClient) {
                // Already loaded, call immediately
                try {
                    callback(global.unifiedAuthClient);
                } catch (error) {
                    console.error('Error in immediate auth load callback:', error);
                }
            } else {
                // Add to callbacks list
                this.callbacks.push(callback);
            }
        }

        /**
         * Register callback for load errors
         * @param {Function} callback - Error callback function
         */
        onError(callback) {
            this.errorCallbacks.push(callback);
        }

        /**
         * Get current auth client if available
         * @returns {Object|null} Auth client or null
         */
        getAuthClient() {
            return global.unifiedAuthClient || null;
        }

        /**
         * Check if auth client is loaded
         * @returns {boolean} True if loaded
         */
        isAuthLoaded() {
            return this.isLoaded && !!global.unifiedAuthClient;
        }
    }

    /**
     * Module Integration Helper
     * Provides easy integration for different modules
     */
    class ModuleAuthIntegration {
        constructor(moduleName) {
            this.moduleName = moduleName;
            this.authClient = null;
            this.eventHandlers = {};
        }

        /**
         * Initialize authentication for the module
         * @param {Object} options - Integration options
         */
        async initialize(options = {}) {
            const {
                onLogin = null,
                onLogout = null,
                onSessionExpired = null,
                onPermissionDenied = null,
                autoRedirect = true
            } = options;

            try {
                // Load auth client
                this.authClient = await authLoader.load();
                
                // Set up event handlers
                if (onLogin) {
                    this.authClient.addEventListener('login', onLogin);
                    this.eventHandlers.login = onLogin;
                }
                
                if (onLogout) {
                    this.authClient.addEventListener('logout', onLogout);
                    this.eventHandlers.logout = onLogout;
                }
                
                if (onSessionExpired) {
                    this.authClient.addEventListener('sessionExpired', onSessionExpired);
                    this.eventHandlers.sessionExpired = onSessionExpired;
                } else if (autoRedirect) {
                    // Default session expired handler
                    const defaultSessionExpiredHandler = () => {
                        console.log(`⏰ ${this.moduleName}: Session expired, redirecting to login`);
                        this.redirectToLogin();
                    };
                    this.authClient.addEventListener('sessionExpired', defaultSessionExpiredHandler);
                    this.eventHandlers.sessionExpired = defaultSessionExpiredHandler;
                }
                
                if (onPermissionDenied) {
                    this.authClient.addEventListener('permissionDenied', onPermissionDenied);
                    this.eventHandlers.permissionDenied = onPermissionDenied;
                }

                console.log(`✅ ${this.moduleName}: Authentication integration initialized`);
                
                // Check if already authenticated
                if (this.authClient.isAuthenticated) {
                    console.log(`✅ ${this.moduleName}: User already authenticated`);
                    if (onLogin) {
                        onLogin({ user: this.authClient.getCurrentUser() });
                    }
                }

                return this.authClient;
            } catch (error) {
                console.error(`❌ ${this.moduleName}: Failed to initialize authentication:`, error);
                throw error;
            }
        }

        /**
         * Check if user is authenticated
         * @returns {boolean} True if authenticated
         */
        isAuthenticated() {
            return this.authClient && this.authClient.isAuthenticated;
        }

        /**
         * Get current user
         * @returns {Object|null} Current user or null
         */
        getCurrentUser() {
            return this.authClient ? this.authClient.getCurrentUser() : null;
        }

        /**
         * Check if user has permission
         * @param {string} permission - Permission to check
         * @returns {boolean} True if user has permission
         */
        hasPermission(permission) {
            return this.authClient ? this.authClient.hasPermission(permission) : false;
        }

        /**
         * Redirect to login page
         */
        redirectToLogin() {
            // Don't redirect Config module - it should show inline login
            if (this.moduleName === 'Config') {
                console.log(`${this.moduleName}: Skipping redirect, showing inline login instead`);
                return;
            }

            const currentPath = window.location.pathname;
            const loginUrl = '/dashboard/index.html';

            if (currentPath !== loginUrl) {
                console.log(`${this.moduleName}: Redirecting to login page`);
                window.location.href = loginUrl;
            }
        }

        /**
         * Cleanup event handlers
         */
        cleanup() {
            if (this.authClient) {
                Object.entries(this.eventHandlers).forEach(([event, handler]) => {
                    this.authClient.removeEventListener(event, handler);
                });
                this.eventHandlers = {};
            }
        }
    }

    // Create singleton auth loader
    const authLoader = new AuthLoader();

    // Export to global scope
    global.AuthLoader = AuthLoader;
    global.ModuleAuthIntegration = ModuleAuthIntegration;
    global.authLoader = authLoader;

    // For module systems
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = { AuthLoader, ModuleAuthIntegration, authLoader };
    }

})(typeof window !== 'undefined' ? window : global);
