class CSRFTokenManager {
  constructor() {
    this.token = null;
    this.cookieName = 'smarttest_csrf';
  }

  _readCookie(name) {
    if (typeof document === 'undefined') return null;
    const match = document.cookie.match(new RegExp('(?:^|; )' + name.replace(/([.$?*|{}()\[\]\\\/\+^])/g, '\\$1') + '=([^;]*)'));
    return match ? decodeURIComponent(match[1]) : null;
  }

  _writeCookie(name, value) {
    if (typeof document === 'undefined') return;
    document.cookie = `${name}=${encodeURIComponent(value)}; path=/`;
  }

  async _fetchToken() {
    try {
      const response = await fetch('/csrf-token', {
        method: 'GET',
        credentials: 'include'
      });
      if (response.ok) {
        const data = await response.json();
        this.token = data.csrfToken;
        this._writeCookie(this.cookieName, this.token);
        return this.token;
      }
    } catch (err) {
      console.warn('Failed to fetch CSRF token', err);
    }
    return null;
  }

  async getToken() {
    if (this.token) return this.token;
    const stored = this._readCookie(this.cookieName);
    if (stored) {
      this.token = stored;
      return stored;
    }
    return await this._fetchToken();
  }

  async includeInRequest(config = {}) {
    const token = await this.getToken();
    if (!config.headers) config.headers = {};
    if (token) config.headers['X-CSRF-Token'] = token;
    return config;
  }

  async handleTokenExpiry() {
    this.token = null;
    this._writeCookie(this.cookieName, '');
    return await this._fetchToken();
  }
}

// Create singleton instance
const csrfTokenManager = new CSRFTokenManager();

// Make globally available
if (typeof window !== 'undefined') {
  window.csrfTokenManager = csrfTokenManager;
}

// Export for CommonJS environments
if (typeof module !== 'undefined' && module.exports) {
  module.exports = csrfTokenManager;
}

console.log('🛡️ CSRFTokenManager initialized');
