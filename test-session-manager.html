<!DOCTYPE html>
<html>
<head>
    <title>Session Manager Test</title>
</head>
<body>
    <h1>Session Manager Test</h1>
    <div id="output"></div>

    <script src="frontend/shared/auth/credential-manager.js"></script>
    <script src="frontend/shared/auth/unified-auth-client.js"></script>
    <script src="frontend/shared/auth/session-manager.js"></script>
    <script src="frontend/shared/auth/universal-auth-check.js"></script>

    <script>
        const output = document.getElementById('output');
        
        function log(message) {
            console.log(message);
            output.innerHTML += '<p>' + message + '</p>';
        }

        // Test session manager loading
        setTimeout(() => {
            log('Testing session manager availability...');
            log('window.sessionManager: ' + !!window.sessionManager);
            log('window.UniversalAuthCheck: ' + !!window.UniversalAuthCheck);
            log('window.unifiedAuthClient: ' + !!window.unifiedAuthClient);
            
            if (window.sessionManager) {
                log('✅ Session manager loaded successfully');
                const session = window.sessionManager.getCurrentSession();
                log('Current session: ' + (session ? 'Found' : 'None'));
            } else {
                log('❌ Session manager not loaded');
            }
            
            if (window.UniversalAuthCheck) {
                log('✅ UniversalAuthCheck loaded successfully');
                const checker = new UniversalAuthCheck('Test');
                log('Created auth checker: ' + !!checker);
            } else {
                log('❌ UniversalAuthCheck not loaded');
            }
        }, 2000);
    </script>
</body>
</html>
