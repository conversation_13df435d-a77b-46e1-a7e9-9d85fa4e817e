PS C:\Dev\smarttest>
PS C:\Dev\smarttest> echo $USE_MOCK_API
PS C:\Dev\smarttest> npm run build

> smarttest@1.3.0 build
> npm run clean && npm run build:dashboard && npm run build:config && npm run build:reports


> smarttest@1.3.0 clean
> npx rimraf frontend/server/public


> smarttest@1.3.0 build:dashboard
> npx copyfiles -u 2 frontend/dashboard/**/* frontend/server/public/dashboard


> smarttest@1.3.0 build:config
> npx copyfiles -u 2 frontend/config/**/* frontend/server/public/config


> smarttest@1.3.0 build:reports
> npx copyfiles -u 2 frontend/reports/**/* frontend/server/public/reports

PS C:\Dev\smarttest> npm start

> smarttest@1.3.0 start
> node frontend/server/api.js

Loaded environment variables from .env.development
✅ Audit Logger initialized
Loaded 7 allowed users from C:\Dev\smarttest\frontend\server\config\allowed-users.json
✅ Session Manager initialized
✅ Account Lockout Manager initialized
🔧 DEVELOPMENT MODE ENABLED
Development features: {
  skipAuth: false,
  useMockApi: false,
  verboseLogging: true,
  logApiCalls: true
}
Initializing API server...
SSH_USER not set. config.SSH_USER: volfkoi, process.env.SSH_USER: volfkoi
Using direct SSH connection (fixed implementation)
Database connection initialized
Server running on port 3000
[2025-08-11T11:06:46.518Z] POST /auth/login from ::1
🔄 New session created - CSRF tokens will be refreshed as needed
✅ Session created: 541e321fff4d0e50c429c156424ec160371efe4642ae4cde58cc0c4908117612 for user: <EMAIL>
✅ AUTH_SUCCESS: <EMAIL> from ::1
✅ Login successful: <EMAIL> from ::1
[2025-08-11T11:06:46.593Z] GET /local/recent-runs?limit=20 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 20 }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [20]
✅ Database Query: Retrieved 20 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 06:56:30, end_ts: 2025-08-11 06:56:46
Row 2 start_ts: 2025-08-11 06:55:06, end_ts: 2025-08-11 06:55:22
Row 3 start_ts: 2025-08-11 06:42:24, end_ts: 2025-08-11 06:43:07
⏳ Processing 20 sessions in 4 batches...
✅ Processed 20 sessions successfully
Retrieved 20 recent runs
[2025-08-11T11:06:53.389Z] GET /local/recent-runs?since_id=18456 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18456
Fetching recent runs with filters: { since_id: 18456 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18456
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [18456,100]
[2025-08-11T11:06:54.512Z] GET /.well-known/appspecific/com.chrome.devtools.json from ::1
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 06:56:30, end_ts: 2025-08-11 06:56:46
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-08-11T11:07:00.598Z] GET /csrf-token from ::1
✅ JWT Token validated for user: <EMAIL>
🔑 CSRF token generated for session: 541e321fff4d0e50c429c156424ec160371efe4642ae4cde58cc0c4908117612
🔑 Token: 38f241d1...
[2025-08-11T11:07:00.607Z] POST /api/suite-runner from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
🛡️ CSRF validation for POST /suite-runner:
   Token provided: YES (38f241d1...)
   Session ID: 541e321fff4d0e50c429c156424ec160371efe4642ae4cde58cc0c4908117612
   Token valid: true
✅ CSRF validation passed for POST /suite-runner
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
🛡️ CSRF validation for POST /suite-runner:
   Token provided: YES (38f241d1...)
   Session ID: 541e321fff4d0e50c429c156424ec160371efe4642ae4cde58cc0c4908117612
   Token valid: true
✅ CSRF validation passed for POST /suite-runner
[API /suite-runner] Received request with params: { ts_id: '323' }
[API /case-runner] Forwarding request to external API: http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/CaseRunner
[Service case-runner] Configuration parameters applied: {
  provided: [ 'ts_id', 'uid', 'password' ],
  defaults_used: [
    'envir',
    'shell_host',
    'file_path',
    'operatorConfigs',
    'kafka_server',
    'dataCenter',
    'rgs_env',
    'old_version',
    'networkType1',
    'networkType2',
    'sign',
    'rate_src'
  ]
}
[Service case-runner] Raw response text received from external API:
--------------------- START RESPONSE ---------------------


<html>
<head>
<title>View Your Test Result</title>
<script type="text/javascript" src="jquery-ui-1.11.0.custom/external/jquery/jquery.js"></script>
<script src="js/lib.js"></script>
<script src="js/menu.js"></script>
<link rel="stylesheet" type="text/css" href="css/table.css">
</head>
<body>
        <a href="home" class="clickable">Home</a> &nbsp;
<span id="case" class="clickable">Case#</span> &nbsp;
<span id="suite" class="clickable">Suite#</span> &nbsp;
<span id="project" class="clickable">Project#</span> &nbsp;
<a href="ReportList?tp_id=201&tp_id=202" class="clickable">Report</a>
<p/>
        Test Result<p/>
        Your test session id: 18458<p/>
        <a href="ReportSummary?tsn_id=18458">View Test Result Summary</a><p/>
        <a href="ReportDetails?tsn_id=18458">View Test Result Details</a><p/>
ID: <a href="ProfileLoader" class="clickable"></a><p/>

</body>
</html>

---------------------- END RESPONSE ----------------------
[2025-08-11T11:07:03.394Z] GET /local/recent-runs?since_id=18456 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18456
Fetching recent runs with filters: { since_id: 18456 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18456
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [18456,100]
[2025-08-11T11:07:04.809Z] POST /api/stop-test from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
🛡️ CSRF validation for POST /stop-test:
   Token provided: YES (38f241d1...)
   Session ID: 541e321fff4d0e50c429c156424ec160371efe4642ae4cde58cc0c4908117612
   Token valid: true
✅ CSRF validation passed for POST /stop-test
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
🛡️ CSRF validation for POST /stop-test:
   Token provided: YES (38f241d1...)
   Session ID: 541e321fff4d0e50c429c156424ec160371efe4642ae4cde58cc0c4908117612
   Token valid: true
✅ CSRF validation passed for POST /stop-test
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
[/api/stop-test][REQ-1754910424816-o5tt8f6vf] ===== STOP TEST API REQUEST =====
[/api/stop-test][REQ-1754910424816-o5tt8f6vf] Request received from client
[/api/stop-test][REQ-1754910424816-o5tt8f6vf] User: <EMAIL>
[/api/stop-test][REQ-1754910424816-o5tt8f6vf] Request body: { tsn_id: '18458' }
[/api/stop-test][REQ-1754910424816-o5tt8f6vf] Request headers: {
  host: 'localhost:3000',
  connection: 'keep-alive',
  'content-length': '18',
  pragma: 'no-cache',
  'cache-control': 'no-cache',
  'sec-ch-ua-platform': '"Windows"',
  'x-csrf-token': '38f241d164e470326d901d4dc572e6d4c27f6b47a2a9c067398eedd70c02d786',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  dnt: '1',
  'content-type': 'application/json',
  'sec-ch-ua-mobile': '?0',
  accept: '*/*',
  origin: 'http://localhost:3000',
  'sec-fetch-site': 'same-origin',
  'sec-fetch-mode': 'cors',
  'sec-fetch-dest': 'empty',
  referer: 'http://localhost:3000/dashboard/',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'en',
  cookie: 'accessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.X6Ml_V2ni4WyE_8YBSearB9F2Q6dbIyRUj0rwQfGE4Y; refreshToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************.Rjf4x5BdJHdb-OCN2zjN53G_qv54unEFsNOYKDZlU2U; sessionId=541e321fff4d0e50c429c156424ec160371efe4642ae4cde58cc0c4908117612; smarttest_csrf=38f241d164e470326d901d4dc572e6d4c27f6b47a2a9c067398eedd70c02d786'
}
[/api/stop-test][REQ-1754910424816-o5tt8f6vf] Using JWT user credentials: <EMAIL>
[/api/stop-test][REQ-1754910424816-o5tt8f6vf] Calling stopTest service with TSN: 18458
[stopTest Service][TSN:18458] ===== STOP TEST REQUEST STARTED =====
[stopTest Service][TSN:18458] Input parameters: { tsnId: '18458', uid: 'iakov.volf...', password: '[PROVIDED]' }
[stopTest Service][TSN:18458] Attempting to get FRESH JSESSIONID cookie for user: <EMAIL>
[stopTest Service][TSN:18458] 🔄 FORCING FRESH LOGIN - bypassing cache for stop operation
[CookieAuth] 🔄 getFreshJsessionId called for user: <EMAIL> (BYPASSING CACHE)
[CookieAuth] 🔄 Forcing fresh login for critical operation
[CookieAuth] 💾 Cached new <NAME_EMAIL>: 79FEC502...
[CookieAuth] 💾 Cache expires at: 2025-08-11T11:37:05.993Z
[CookieAuth] 💾 Cache expires in: 1800s
[stopTest Service][TSN:18458] Successfully obtained FRESH JSESSIONID: 79FEC502E9...
[stopTest Service][TSN:18458] Prepared request: {
  url: 'http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/RemoveSession',
  method: 'POST',
  formData: 'tsn_id=18458',
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    Cookie: 'JSESSIONID=79FEC502E9...'
  }
}
[stopTest Service][TSN:18458] ===== SENDING HTTP REQUEST =====
[stopTest Service][TSN:18458] ===== HTTP RESPONSE RECEIVED =====
[stopTest Service][TSN:18458] Request duration: 501ms
[stopTest Service][TSN:18458] Response status: 200 OK
[stopTest Service][TSN:18458] Response headers: { 'content-length': '5', date: 'Mon, 11 Aug 2025 11:07:06 GMT' }
[stopTest Service][TSN:18458] ===== RESPONSE BODY ANALYSIS =====
[stopTest Service][TSN:18458] Raw response text: 'Error'
[stopTest Service][TSN:18458] Response length: 5 characters
[stopTest Service][TSN:18458] Response trimmed: 'Error'
[stopTest Service][TSN:18458] Response type: string
[stopTest Service][TSN:18458] Response is empty: false
[stopTest Service][TSN:18458] ===== RESPONSE ANALYSIS =====
[stopTest Service][TSN:18458] ⚠️  AMBIGUOUS RESPONSE: External API returned 'Error'
[stopTest Service][TSN:18458] This could mean:
[stopTest Service][TSN:18458]   - Test was already stopped
[stopTest Service][TSN:18458]   - Test doesn't exist
[stopTest Service][TSN:18458]   - Test is in a state that can't be stopped
[stopTest Service][TSN:18458]   - Authentication/permission issue
[stopTest Service][TSN:18458]   - Internal error in external API
[stopTest Service][TSN:18458] Attempting to verify actual test state...
[CookieAuth] 🔑 getJsessionId called for user: <EMAIL>
[CookieAuth] 🔑 Found cached session, expires in: 1799s
[CookieAuth] 🔑 ✅ Using cached JSESSIONID: 79FEC502...
[stopTest Service][TSN:18458] Status check result: {
  tsn_id: '18458',
  status: 'running',
  start_time: '2025-08-11 11:07:01',
  end_time: '2025-08-11 11:07:07',
  progress: 0
}
[stopTest Service][TSN:18458] ⚠️  FAILED: Test is still running (status: running)
[stopTest Service][TSN:18458] ===== STOP TEST REQUEST COMPLETED =====
[/api/stop-test][REQ-1754910424816-o5tt8f6vf] ===== SERVICE RESPONSE =====
[/api/stop-test][REQ-1754910424816-o5tt8f6vf] Service result: {
  success: false,
  status: 'running',
  message: "Failed to stop test run 18458. External API returned 'Error' and status verification shows the test is still running.",
  actualResult: 'FAILED_TO_STOP',
  originalResponse: 'Error',
  verifiedStatus: 'running'
}
[/api/stop-test][REQ-1754910424816-o5tt8f6vf] Sending response to client: {
  success: false,
  status: 'running',
  message: "Failed to stop test run 18458. External API returned 'Error' and status verification shows the test is still running.",
  actualResult: 'FAILED_TO_STOP',
  originalResponse: 'Error',
  verifiedStatus: 'running'
}
[/api/stop-test][REQ-1754910424816-o5tt8f6vf] ===== STOP TEST API REQUEST COMPLETED =====
✅ Database Query: Retrieved 2 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:07:01, end_ts: 2025-08-11 11:07:07
Row 2 start_ts: 2025-08-11 06:56:30, end_ts: 2025-08-11 06:56:46
⏳ Processing 2 sessions in 1 batches...
✅ Processed 2 sessions successfully
Retrieved 2 recent runs
[2025-08-11T11:07:13.393Z] GET /local/recent-runs?since_id=18457 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18457
Fetching recent runs with filters: { since_id: 18457 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18457
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [18457,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:07:01, end_ts: 2025-08-11 11:07:17
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-08-11T11:07:23.404Z] GET /local/recent-runs?since_id=18457 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18457
Fetching recent runs with filters: { since_id: 18457 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18457
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [18457,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:07:01, end_ts: 2025-08-11 11:07:17
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-08-11T11:07:33.769Z] GET /local/recent-runs?since_id=18457 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18457
Fetching recent runs with filters: { since_id: 18457 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18457
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL: 
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [18457,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:07:01, end_ts: 2025-08-11 11:07:17
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-08-11T11:07:43.777Z] GET /local/recent-runs?since_id=18457 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18457
Fetching recent runs with filters: { since_id: 18457 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18457
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [18457,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:07:01, end_ts: 2025-08-11 11:07:17
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-08-11T11:07:49.483Z] GET /local/recent-runs? from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: {}
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:07:01, end_ts: 2025-08-11 11:07:17
Row 2 start_ts: 2025-08-11 06:56:30, end_ts: 2025-08-11 06:56:46
Row 3 start_ts: 2025-08-11 06:55:06, end_ts: 2025-08-11 06:55:22
⏳ Processing 100 sessions in 20 batches...
