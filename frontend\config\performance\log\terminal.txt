     WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [18457,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:07:01, end_ts: 2025-08-11 11:07:17
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-08-11T11:09:53.763Z] GET /local/recent-runs?since_id=18457 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18457
Fetching recent runs with filters: { since_id: 18457 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18457
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [18457,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:07:01, end_ts: 2025-08-11 11:07:17
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-08-11T11:10:03.766Z] GET /local/recent-runs?since_id=18457 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18457
Fetching recent runs with filters: { since_id: 18457 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18457
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [18457,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:07:01, end_ts: 2025-08-11 11:07:17
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-08-11T11:10:09.478Z] GET /local/recent-runs? from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: {}
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:07:01, end_ts: 2025-08-11 11:07:17
Row 2 start_ts: 2025-08-11 06:56:30, end_ts: 2025-08-11 06:56:46
Row 3 start_ts: 2025-08-11 06:55:06, end_ts: 2025-08-11 06:55:22
⏳ Processing 100 sessions in 20 batches...
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[2025-08-11T11:10:23.764Z] GET /local/recent-runs?since_id=18457 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18457
Fetching recent runs with filters: { since_id: 18457 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18457
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [18457,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:07:01, end_ts: 2025-08-11 11:07:17
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-08-11T11:10:38.776Z] GET /local/recent-runs?since_id=18457 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18457
Fetching recent runs with filters: { since_id: 18457 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18457
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [18457,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:07:01, end_ts: 2025-08-11 11:07:17
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-08-11T11:11:38.768Z] GET /local/recent-runs?since_id=18457 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18457
Fetching recent runs with filters: { since_id: 18457 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18457
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [18457,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:07:01, end_ts: 2025-08-11 11:07:17
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-08-11T11:11:46.767Z] GET /auth/validate from ::1
[2025-08-11T11:12:38.769Z] GET /local/recent-runs?since_id=18457 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18457
Fetching recent runs with filters: { since_id: 18457 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18457
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [18457,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:07:01, end_ts: 2025-08-11 11:07:17
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-08-11T11:13:27.945Z] GET /local/recent-runs?since_id=18457 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18457
Fetching recent runs with filters: { since_id: 18457 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18457
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [18457,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:07:01, end_ts: 2025-08-11 11:07:17
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-08-11T11:13:33.768Z] GET /local/recent-runs?since_id=18457 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18457
Fetching recent runs with filters: { since_id: 18457 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18457
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [18457,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:07:01, end_ts: 2025-08-11 11:07:17
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-08-11T11:13:39.481Z] GET /local/recent-runs? from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: {}
PS C:\Dev\smarttest> npm start

> smarttest@1.3.0 start      
> node frontend/server/api.js

Loaded environment variables from .env.development
✅ Audit Logger initialized
Loaded 7 allowed users from C:\Dev\smarttest\frontend\server\config\allowed-users.json
✅ Session Manager initialized
✅ Account Lockout Manager initialized
🔧 DEVELOPMENT MODE ENABLED
Development features: {
  skipAuth: false,     
  useMockApi: false,   
  verboseLogging: true,
  logApiCalls: true    
}
Initializing API server...
SSH_USER not set. config.SSH_USER: volfkoi, process.env.SSH_USER: volfkoi
Using direct SSH connection (fixed implementation)
Database connection initialized
Server running on port 3000
[2025-08-11T11:14:03.778Z] GET /local/recent-runs?since_id=18457 from ::1
❌ JWT Token validation failed: Session not found
[2025-08-11T11:14:13.776Z] GET /local/recent-runs?since_id=18457 from ::1
❌ JWT Token validation failed: Session not found
[2025-08-11T11:14:23.411Z] GET /local/recent-runs?since_id=18457 from ::1
❌ JWT Token validation failed: Session not found
[2025-08-11T11:14:45.786Z] POST /auth/login from ::1
🔄 New session created - CSRF tokens will be refreshed as needed
✅ Session created: 80ea914d1a7e509c5da918f350562e6b4d4d1db2d49024e47f5c99eee4056c0d for user: <EMAIL>
✅ AUTH_SUCCESS: <EMAIL> from ::1
✅ Login successful: <EMAIL> from ::1
[2025-08-11T11:14:46.055Z] GET /local/recent-runs?limit=20 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 20 }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [20]
✅ Database Query: Retrieved 20 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:07:01, end_ts: 2025-08-11 11:07:17
Row 2 start_ts: 2025-08-11 06:56:30, end_ts: 2025-08-11 06:56:46
Row 3 start_ts: 2025-08-11 06:55:06, end_ts: 2025-08-11 06:55:22
⏳ Processing 20 sessions in 4 batches...
✅ Processed 20 sessions successfully
Retrieved 20 recent runs
[2025-08-11T11:14:52.798Z] GET /local/recent-runs?since_id=18457 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18457
Fetching recent runs with filters: { since_id: 18457 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18457
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [18457,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:07:01, end_ts: 2025-08-11 11:07:17
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-08-11T11:15:02.803Z] GET /local/recent-runs?since_id=18457 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18457
Fetching recent runs with filters: { since_id: 18457 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18457
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [18457,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:07:01, end_ts: 2025-08-11 11:07:17
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-08-11T11:15:12.810Z] GET /local/recent-runs?since_id=18457 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18457
Fetching recent runs with filters: { since_id: 18457 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18457
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [18457,100]
[2025-08-11T11:15:17.347Z] GET /.well-known/appspecific/com.chrome.devtools.json from ::1
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:07:01, end_ts: 2025-08-11 11:07:17
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-08-11T11:15:20.953Z] GET /csrf-token from ::1
✅ JWT Token validated for user: <EMAIL>
🔑 CSRF token generated for session: 80ea914d1a7e509c5da918f350562e6b4d4d1db2d49024e47f5c99eee4056c0d
🔑 Token: 41f46aa1...
[2025-08-11T11:15:20.963Z] POST /api/suite-runner from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
🛡️ CSRF validation for POST /suite-runner:
   Token provided: YES (41f46aa1...)
   Session ID: 80ea914d1a7e509c5da918f350562e6b4d4d1db2d49024e47f5c99eee4056c0d
   Token valid: true
✅ CSRF validation passed for POST /suite-runner
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
🛡️ CSRF validation for POST /suite-runner:
   Token provided: YES (41f46aa1...)
   Session ID: 80ea914d1a7e509c5da918f350562e6b4d4d1db2d49024e47f5c99eee4056c0d
   Token valid: true
✅ CSRF validation passed for POST /suite-runner
[API /suite-runner] Received request with params: { ts_id: '327' }
[API /case-runner] Forwarding request to external API: http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/CaseRunner
[Service case-runner] Configuration parameters applied: {
  provided: [ 'ts_id', 'uid', 'password' ],
  defaults_used: [
    'envir',
    'shell_host',
    'file_path',
    'operatorConfigs',
    'kafka_server',
    'dataCenter',
    'rgs_env',
    'old_version',
    'networkType1',
    'networkType2',
    'sign',
    'rate_src'
  ]
}
[Service case-runner] Raw response text received from external API:
--------------------- START RESPONSE ---------------------


<html>
<head>
<title>View Your Test Result</title>
<script type="text/javascript" src="jquery-ui-1.11.0.custom/external/jquery/jquery.js"></script>
<script src="js/lib.js"></script>
<script src="js/menu.js"></script>
<link rel="stylesheet" type="text/css" href="css/table.css">
</head>
<body>
        <a href="home" class="clickable">Home</a> &nbsp;
<span id="case" class="clickable">Case#</span> &nbsp;
<span id="suite" class="clickable">Suite#</span> &nbsp;
<span id="project" class="clickable">Project#</span> &nbsp;
<a href="ReportList?tp_id=201&tp_id=202" class="clickable">Report</a>
<p/>
        Test Result<p/>
        Your test session id: 18459<p/>
        <a href="ReportSummary?tsn_id=18459">View Test Result Summary</a><p/>
        <a href="ReportDetails?tsn_id=18459">View Test Result Details</a><p/>
ID: <a href="ProfileLoader" class="clickable"></a><p/>

</body>
</html>

---------------------- END RESPONSE ----------------------
[2025-08-11T11:15:22.811Z] GET /local/recent-runs?since_id=18457 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18457
Fetching recent runs with filters: { since_id: 18457 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18457
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [18457,100]
[2025-08-11T11:15:23.514Z] GET /local/recent-runs?limit=20 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 20 }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [20]
✅ Database Query: Retrieved 2 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:15:21, end_ts: 2025-08-11 11:15:21
Row 2 start_ts: 2025-08-11 11:07:01, end_ts: 2025-08-11 11:07:17
⏳ Processing 2 sessions in 1 batches...
✅ Processed 2 sessions successfully
Retrieved 2 recent runs
✅ Database Query: Retrieved 20 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:15:21, end_ts: 2025-08-11 11:15:21
Row 2 start_ts: 2025-08-11 11:07:01, end_ts: 2025-08-11 11:07:17
Row 3 start_ts: 2025-08-11 06:56:30, end_ts: 2025-08-11 06:56:46
⏳ Processing 20 sessions in 4 batches...
✅ Processed 20 sessions successfully
Retrieved 20 recent runs
[2025-08-11T11:15:30.066Z] GET /local/recent-runs?since_id=18458 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18458
Fetching recent runs with filters: { since_id: 18458 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18458
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [18458,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:15:21, end_ts: 2025-08-11 11:15:32
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-08-11T11:15:40.072Z] GET /local/recent-runs?since_id=18458 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18458
Fetching recent runs with filters: { since_id: 18458 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18458
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [18458,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:15:21, end_ts: 2025-08-11 11:15:43
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-08-11T11:15:45.793Z] GET /local/recent-runs? from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: {}
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:15:21, end_ts: 2025-08-11 11:15:48
Row 2 start_ts: 2025-08-11 11:07:01, end_ts: 2025-08-11 11:07:17
Row 3 start_ts: 2025-08-11 06:56:30, end_ts: 2025-08-11 06:56:46
⏳ Processing 100 sessions in 20 batches...
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[2025-08-11T11:16:00.073Z] GET /local/recent-runs?since_id=18458 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18458
Fetching recent runs with filters: { since_id: 18458 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18458
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [18458,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:15:21, end_ts: 2025-08-11 11:15:48
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
[2025-08-11T11:16:10.771Z] GET /local/recent-runs?since_id=18458 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
[recent-runs] Filtering with since_id: 18458
Fetching recent runs with filters: { since_id: 18458 }
[GET_RECENT_RUNS] Adding since_id filter: tsn_id > 18458
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id) AS total_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'P') AS passed_cases,
             (SELECT COUNT(DISTINCT tr.tc_id)
              FROM test_result tr
              WHERE tr.tsn_id = ts.tsn_id AND tr.outcome = 'F') AS failed_cases
      FROM test_session ts
     WHERE ts.tsn_id > ? ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [18458,100]
✅ Database Query: Retrieved 1 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-08-11 11:15:21, end_ts: 2025-08-11 11:15:48
⏳ Processing 1 sessions in 1 batches...
✅ Processed 1 sessions successfully
Retrieved 1 recent runs
