/**
 * Shared Credential Manager for SmartTest Application
 * 
 * Provides unified credential storage, retrieval, and management
 * across all modules with consistent error handling and fallback logic.
 */

class CredentialManager {
  constructor() {
    this.credentials = { uid: '', password: '' };
    this.storageKeys = {
      uid: 'smarttest_uid',
      password: 'smarttest_pwd',
      // Legacy keys for backward compatibility
      legacyUid: 'currentUser',
      legacyCredentials: 'userCredentials'
    };
  }

  /**
   * Set credentials with unified storage logic
   * @param {string} username - Username
   * @param {string} password - Password
   * @returns {boolean} Whether credentials were successfully set
   */
  setCredentials(username, password) {
    // Always set in memory first
    this.credentials = { uid: username, password: password };
    
    // Attempt to store in sessionStorage with comprehensive error handling
    const storageResult = this._storeCredentials(username, password);
    
    console.log(`Credentials set for user: ${username}, Storage success: ${storageResult.success}`);
    if (!storageResult.success) {
      console.warn('Credential storage failed:', storageResult.error);
    }
    
    return true; // Always return true since memory storage always works
  }

  /**
   * Load credentials with comprehensive fallback logic
   * @returns {boolean} Whether credentials were successfully loaded
   */
  loadCredentials() {
    try {
      const result = this._loadFromStorage();
      
      if (result.success) {
        this.credentials = { uid: result.uid, password: result.password };
        console.log(`Credentials loaded for user: ${result.uid} (source: ${result.source})`);
        return true;
      }
      
      console.log('No valid credentials found in any storage location');
      this.credentials = { uid: '', password: '' };
      return false;
    } catch (error) {
      console.error('Error loading credentials:', error);
      this.credentials = { uid: '', password: '' };
      return false;
    }
  }

  /**
   * Clear all stored credentials
   */
  clearCredentials() {
    // Clear memory
    this.credentials = { uid: '', password: '' };
    
    // Clear all storage locations
    this._clearAllStorage();
    
    console.log('All credentials cleared');
  }

  /**
   * Get current credentials
   * @returns {Object} Current credentials object
   */
  getCredentials() {
    return { ...this.credentials };
  }

  /**
   * Check if credentials are available
   * @returns {boolean} Whether valid credentials exist
   */
  hasCredentials() {
    return !!(this.credentials.uid && this.credentials.password);
  }

  /**
   * Get authentication parameters for API calls
   * @returns {Object} Auth parameters object
   */
  getAuthParams() {
    return {
      uid: this.credentials.uid,
      password: this.credentials.password
    };
  }

  /**
   * Private method: Store credentials with fallback logic
   * @private
   */
  _storeCredentials(username, password) {
    let success = false;
    let error = null;

    // Primary: Try sessionStorage
    try {
      sessionStorage.setItem(this.storageKeys.uid, username);
      sessionStorage.setItem(this.storageKeys.password, password);
      success = true;
    } catch (sessionError) {
      error = `SessionStorage failed: ${sessionError.message}`;
      
      // Fallback: Try parent window sessionStorage (for iframe contexts)
      if (window.parent && window.parent !== window) {
        try {
          window.parent.sessionStorage?.setItem(this.storageKeys.uid, username);
          window.parent.sessionStorage?.setItem(this.storageKeys.password, password);
          success = true;
          error = null;
        } catch (parentError) {
          error += `, Parent window failed: ${parentError.message}`;
        }
      }
    }

    return { success, error };
  }

  /**
   * Private method: Load credentials with comprehensive fallback
   * @private
   */
  _loadFromStorage() {
    // Priority 1: Standard sessionStorage keys
    try {
      const uid = sessionStorage.getItem(this.storageKeys.uid);
      const password = sessionStorage.getItem(this.storageKeys.password);
      if (uid) { // Allow empty password for JWT authentication
        return { success: true, uid, password: password || '', source: 'sessionStorage (standard)' };
      }
    } catch (error) {
      console.log('Cannot access sessionStorage, trying alternatives');
    }

    // Priority 2: Legacy sessionStorage keys
    try {
      const legacyUid = sessionStorage.getItem(this.storageKeys.legacyUid);
      if (legacyUid) {
        return { success: true, uid: legacyUid, password: '', source: 'sessionStorage (legacy)' };
      }
    } catch (error) {
      // Continue to next fallback
    }

    // Priority 3: Standard localStorage keys
    try {
      const uid = localStorage.getItem(this.storageKeys.uid);
      const password = localStorage.getItem(this.storageKeys.password);
      if (uid) { // Allow empty password for JWT authentication
        return { success: true, uid, password: password || '', source: 'localStorage (standard)' };
      }
    } catch (error) {
      // Continue to next fallback
    }

    // Priority 4: Legacy localStorage credentials
    try {
      const legacyCredentials = localStorage.getItem(this.storageKeys.legacyCredentials);
      if (legacyCredentials) {
        const parsed = JSON.parse(legacyCredentials);
        if (parsed.uid) {
          return { 
            success: true, 
            uid: parsed.uid, 
            password: parsed.password || '', 
            source: 'localStorage (legacy)' 
          };
        }
      }
    } catch (error) {
      // Continue to next fallback
    }

    // Priority 5: Legacy currentUser in localStorage
    try {
      const legacyUid = localStorage.getItem(this.storageKeys.legacyUid);
      if (legacyUid) {
        return { success: true, uid: legacyUid, password: '', source: 'localStorage (legacy uid)' };
      }
    } catch (error) {
      // No more fallbacks
    }

    return { success: false };
  }

  /**
   * Private method: Clear all storage locations
   * @private
   */
  _clearAllStorage() {
    const keysToRemove = [
      this.storageKeys.uid,
      this.storageKeys.password,
      this.storageKeys.legacyUid,
      this.storageKeys.legacyCredentials,
      // Additional auth-related keys
      'smarttest_session_id',
      'smarttest_session_expiry',
      'smarttest_user',
      'smarttest_permissions',
      'smarttest_auth'
    ];

    // Clear sessionStorage
    keysToRemove.forEach(key => {
      try {
        sessionStorage.removeItem(key);
      } catch (error) {
        // Ignore errors
      }
    });

    // Clear localStorage
    keysToRemove.forEach(key => {
      try {
        localStorage.removeItem(key);
      } catch (error) {
        // Ignore errors
      }
    });
  }
}

// Create singleton instance
const credentialManager = new CredentialManager();

// Export for module usage
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
  module.exports = credentialManager;
}

// Make globally available
window.credentialManager = credentialManager;

console.log('✅ Shared Credential Manager initialized');
