(function(global) {
  function getCredentials() {
    if (global.unifiedAuthClient && global.unifiedAuthClient.isAuthenticated && global.unifiedAuthClient.currentUser) {
      const user = global.unifiedAuthClient.currentUser;
      if (user && user.uid) {
        let password = user.password || user.apiKey || user.token;
        if (!password) {
          password = 'test';
        }
        return { uid: user.uid, password };
      }
    }
    if (global.credentialManager && typeof global.credentialManager.getCredentials === 'function') {
      const creds = global.credentialManager.getCredentials();
      if (creds && creds.uid && creds.password) {
        return creds;
      }
    }
    try {
      const uid = global.sessionStorage ? global.sessionStorage.getItem('smarttest_uid') : null;
      const password = global.sessionStorage ? global.sessionStorage.getItem('smarttest_pwd') : null;
      if (uid && password) {
        return { uid, password };
      }
    } catch (e) {
      /* session storage unavailable */
    }
    return null;
  }

  function onAvailable(callback) {
    const creds = getCredentials();
    if (creds) {
      callback(creds);
      return;
    }
    const handler = () => {
      const c = getCredentials();
      if (c) {
        global.removeEventListener('auth:login', handler);
        callback(c);
      }
    };
    global.addEventListener('auth:login', handler);
  }

  global.reportsCredentials = { get: getCredentials, onAvailable };
})(window);
