<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>On demand Test Runner</title>
    <!-- Replace Bootstrap with Fluent UI -->
    <link href="https://static2.sharepointonline.com/files/fabric/office-ui-fabric-core/11.0.0/css/fabric.min.css" rel="stylesheet">
    <link href="https://res-1.cdn.office.net/files/fabric-cdn-prod_20230815.002/office-ui-fabric-core/11.0.0/css/fabric.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <!-- Custom Test Search Styles -->
    <link rel="stylesheet" href="css/test-search.css" />
    <!-- Test Details Modal Styles -->
    <link rel="stylesheet" href="css/test-details-modal.css">
    <!-- Load authentication system first -->
    <script src="../shared/auth/auth-loader.js"></script>
    <script src="../shared/auth/credential-manager.js"></script>
    <script src="../shared/auth/unified-auth-client.js"></script>
    <script src="../shared/auth/session-manager.js"></script>
    <script src="../shared/auth/universal-auth-check.js"></script>
    <script src="../shared/auth/event-bus.js"></script>
    <script src="../shared/security/csrf-token-manager.js"></script>
    <script>
        // Initialize CSRF token manager
        if (typeof CSRFTokenManager !== 'undefined') {
            window.csrfTokenManager = new CSRFTokenManager();
            console.log('✅ Config: CSRF token manager initialized');
        } else {
            console.warn('❌ Config: CSRF token manager not available');
        }
    </script>
    <!-- Load shared services in the correct order -->
    <script type="module" src="../shared/services/base-api-service.js"></script>
    <script src="../shared/services/unified-api-service.js"></script>
    <script src="../shared/services/external-api-service.js"></script>
</head>
<body class="ms-Fabric">
    <!-- Login Modal for Config Authentication -->
    <div id="config-login-modal" class="config-modal">
        <div class="config-modal-overlay">
            <div class="config-modal-content">
                <h3 class="config-modal-title">Login Required</h3>
                <p class="config-modal-subtitle">Please log in to access the Custom Test Runner</p>
                <form id="config-login-form">
                    <div class="config-form-group">
                        <input type="email" id="config-username" class="config-input" placeholder="Username" required>
                    </div>
                    <div class="config-form-group">
                        <input type="password" id="config-password" class="config-input" placeholder="Password" required>
                    </div>
                    <div class="config-form-actions">
                        <button type="submit" class="ms-Button ms-Button--primary config-login-btn">Login</button>
                    </div>
                    <div id="config-login-error" class="config-error-message" style="display: none;"></div>
                </form>
            </div>
        </div>
    </div>

    <header class="ms-header">
        <div class="ms-header-title">
            <a class="ms-header-brand" href="#">Test Automation Framework</a>
        </div>
        <div class="ms-header-controls">
            <span class="ms-environment-display" id="environment-display">Environment: Development</span>
            <span class="ms-user-info" id="user-display">Not logged in</span>
            <button class="ms-Button ms-Button--default" id="login-button">
                <span class="ms-Button-label">Login</span>
            </button>
            <button class="ms-Button ms-Button--default" id="logout-button" style="display: none;">
                <span class="ms-Button-label">Logout</span>
            </button>
        </div>
    </header>

    <div class="ms-container">
        <div id="notification-container" style="position: fixed; top: 70px; right: 20px; z-index: 9999; width: 350px;"></div>
        <div class="ms-layout">
            <nav id="sidebarMenu" class="ms-nav">
                <div class="ms-nav-content">
                    <ul class="ms-nav-list">
                        <li class="ms-nav-item">
                            <a class="ms-nav-link" href="../dashboard/index.html">
                                Dashboard
                            </a>
                        </li>
                        <li class="ms-nav-item">
                            <a class="ms-nav-link ms-nav-link-active" href="#">
                                On demand Test Runner
                            </a>
                        </li>
                        <li class="ms-nav-item">
                            <a class="ms-nav-link" href="../reports/index.html">
                                Reports
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="ms-content">
                <div class="ms-content-header">
                    <h1 class="ms-font-xxl">On demand Test Runner</h1>
                </div>



                <div class="ms-grid">
                    <div class="ms-grid-row">
                        <div class="ms-grid-col ms-sm8">
                            <!-- Test Case Search Card -->
                            <div class="ms-card">
                                <div class="ms-card-header">
                                    <h4 class="ms-font-l">Search Test Cases</h4>
                                </div>
                                <div class="ms-card-body">
                                    <form id="test-search-form">
                                        <div class="ms-form-group">
                                            <label class="ms-Label">Test Case ID</label>
                                            <input type="text" class="ms-TextField-field" id="test-case-id-search" placeholder="Search by test case ID">
                                        </div>

                                        <div class="ms-form-group">
                                            <label class="ms-Label">Test Case Name</label>
                                            <input type="text" class="ms-TextField-field" id="test-case-name-search" placeholder="Search by name">
                                        </div>

                                        <div class="ms-form-group">
                                            <label class="ms-Label">Test Case Comment</label>
                                            <input type="text" class="ms-TextField-field" id="test-case-comment-search" placeholder="Search in comments (e.g., 'payout')">
                                            <div class="ms-TextField-description">Search for keywords in test case comments</div>
                                        </div>

                                        <div class="ms-form-group">
                                            <label class="ms-Label">Status</label>
                                            <div class="ms-Dropdown">
                                                <select class="ms-Dropdown-select" id="test-case-status-filter">
                                                    <option value="">All</option>
                                                    <option value="Active">Active</option>
                                                    <option value="Inactive">Inactive</option>
                                                    <option value="Maintenance">Maintenance</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="ms-form-group">
                                            <button type="button" class="ms-Button ms-Button--primary" id="search-test-cases-btn">
                                                <span class="ms-Button-label">Search</span>
                                            </button>
                                            <button type="reset" class="ms-Button ms-Button--default">
                                                <span class="ms-Button-label">Clear</span>
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- Search Results Card -->
                            <div class="ms-card" id="search-results-card" style="display: none;">
                                <div class="ms-card-header">
                                    <h4 class="ms-font-l">Search Results</h4>
                                    <div class="ms-card-activity">
                                        <span class="ms-card-count" id="search-results-count">0</span> test cases found
                                    </div>
                                </div>
                                <div class="ms-card-body">
                                    <div id="search-results-container" class="search-results">
                                        <div class="ms-panel-content">
                                            <div class="ms-empty-message">No results yet. Use the search form above.</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Test Case Runner Card -->
                            <div class="ms-card">
                                <div class="ms-card-header">
                                    <h4 class="ms-font-l">Run Test Case</h4>
                                </div>
                                <div class="ms-card-body">
                                    <form id="test-run-form">
                                        <div class="ms-form-group">
                                            <label class="ms-Label">Test Case ID</label>
                                            <input type="text" class="ms-TextField-field" id="test-case-id" placeholder="Enter test case ID (e.g. 3180)">
                                            <div class="ms-TextField-description">Specify the ID of the test case you want to run</div>
                                        </div>

                                        <!-- Projects Dropdown (maps to QA environments) -->
                                        <h5 class="ms-font-m ms-section-title">Projects</h5>
                                        <div class="ms-form-group">
                                            <div class="ms-Dropdown" id="projects-selector">
                                                <select class="ms-Dropdown-select" id="project-select">
                                                    <option value="qa02">RGS tests</option>
                                                    <option value="qa03">Other</option>
                                                </select>
                                                <div class="ms-TextField-description">Select project type to determine test environment</div>
                                            </div>
                                        </div>

                                        <!-- Shell Host Selection -->
                                        <h5 class="ms-font-m ms-section-title">Shell Host</h5>
                                        <div class="ms-form-group">
                                            <div class="ms-Dropdown">
                                                <select class="ms-Dropdown-select" id="shell-host-select">
                                                    <option value="jps-qa10-app01" selected>jps-qa10-app01</option>
                                                    <option value="jps-qa20-app01">jps-qa20-app01</option>
                                                </select>
                                                <div class="ms-TextField-description">Select the shell host for test execution</div>
                                            </div>
                                        </div>

                                        <!-- Advanced Configuration Section -->
                                        <div class="ms-form-group">
                                            <h5 class="ms-font-m ms-section-title">
                                                <button type="button" class="ms-Button ms-Button--default" id="advanced-config-toggle" style="background: none; border: none; padding: 0; color: inherit; text-align: left; width: 100%;">
                                                    <span class="ms-Button-label">
                                                        <span id="advanced-config-icon">▶</span> Advanced Configuration
                                                    </span>
                                                </button>
                                            </h5>
                                            <div id="advanced-config-section" style="display: none; margin-top: 15px; padding: 15px; border: 1px solid #e1e1e1; border-radius: 4px; background-color: #f9f9f9;">
                                                <div class="ms-TextField-description" style="margin-bottom: 15px; color: #666;">
                                                    Override default configuration parameters for this test run. Leave fields empty to use system defaults.
                                                </div>

                                                <!-- File Path -->
                                                <div class="ms-form-group">
                                                    <label class="ms-Label">File Path</label>
                                                    <input type="text" class="ms-TextField-field" id="file-path-input" placeholder="/home/<USER>/">
                                                    <div class="ms-TextField-description">File system path for test execution</div>
                                                </div>

                                                <!-- Operator Configs -->
                                                <div class="ms-form-group">
                                                    <label class="ms-Label">Operator Configs</label>
                                                    <div class="ms-Dropdown">
                                                        <select class="ms-Dropdown-select" id="operator-configs-select">
                                                            <option value="">Use default</option>
                                                            <option value="operatorNameConfigs">operatorNameConfigs</option>
                                                            <option value="operatorIdConfigs">operatorIdConfigs</option>
                                                        </select>
                                                        <div class="ms-TextField-description">Operator configuration type</div>
                                                    </div>
                                                </div>

                                                <!-- Kafka Server -->
                                                <div class="ms-form-group">
                                                    <label class="ms-Label">Kafka Server</label>
                                                    <input type="text" class="ms-TextField-field" id="kafka-server-input" placeholder="kafka-qa-a0.lab.wagerworks.com">
                                                    <div class="ms-TextField-description">Kafka server hostname</div>
                                                </div>

                                                <!-- Data Center -->
                                                <div class="ms-form-group">
                                                    <label class="ms-Label">Data Center</label>
                                                    <div class="ms-Dropdown">
                                                        <select class="ms-Dropdown-select" id="data-center-select">
                                                            <option value="">Use default</option>
                                                            <option value="GU">GU</option>
                                                            <option value="US">US</option>
                                                            <option value="EU">EU</option>
                                                        </select>
                                                        <div class="ms-TextField-description">Data center location</div>
                                                    </div>
                                                </div>

                                                <!-- Old Version -->
                                                <div class="ms-form-group">
                                                    <label class="ms-Label">Old Version</label>
                                                    <input type="text" class="ms-TextField-field" id="old-version-input" placeholder="0">
                                                    <div class="ms-TextField-description">Version number for compatibility testing</div>
                                                </div>

                                                <!-- Network Types -->
                                                <div class="ms-grid-row">
                                                    <div class="ms-grid-col ms-sm6">
                                                        <div class="ms-form-group">
                                                            <label class="ms-Label">Network Type 1</label>
                                                            <div class="ms-Dropdown">
                                                                <select class="ms-Dropdown-select" id="network-type1-select">
                                                                    <option value="">Use default</option>
                                                                    <option value="multi-site">multi-site</option>
                                                                    <option value="single-site">single-site</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="ms-grid-col ms-sm6">
                                                        <div class="ms-form-group">
                                                            <label class="ms-Label">Network Type 2</label>
                                                            <div class="ms-Dropdown">
                                                                <select class="ms-Dropdown-select" id="network-type2-select">
                                                                    <option value="">Use default</option>
                                                                    <option value="multi-site">multi-site</option>
                                                                    <option value="single-site">single-site</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Sign and Rate Source -->
                                                <div class="ms-grid-row">
                                                    <div class="ms-grid-col ms-sm6">
                                                        <div class="ms-form-group">
                                                            <label class="ms-Label">Sign</label>
                                                            <div class="ms-Dropdown">
                                                                <select class="ms-Dropdown-select" id="sign-select">
                                                                    <option value="">Use default</option>
                                                                    <option value="-">-</option>
                                                                    <option value="+">+</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="ms-grid-col ms-sm6">
                                                        <div class="ms-form-group">
                                                            <label class="ms-Label">Rate Source</label>
                                                            <div class="ms-Dropdown">
                                                                <select class="ms-Dropdown-select" id="rate-src-select">
                                                                    <option value="">Use default</option>
                                                                    <option value="local">local</option>
                                                                    <option value="remote">remote</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="ms-TextField-description" style="margin-top: 10px; font-style: italic; color: #666;">
                                                    💡 Tip: These parameters override the system defaults from app-config.js. The RGS Environment will automatically match the selected Project environment.
                                                </div>
                                            </div>
                                        </div>

                                        <div class="ms-form-group">
                                            <button type="button" class="ms-Button ms-Button--primary" id="run-test-btn">
                                                <span class="ms-Button-label">Run Test</span>
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- Active Tests Panel -->
                            <div class="ms-card">
                                <div class="ms-card-header">
                                    <h4 class="ms-font-l">Active Tests</h4>
                                    <div class="ms-card-activity">
                                        <span class="ms-card-count" id="active-tests-count">0</span> active tests
                                    </div>
                                </div>
                                <div class="ms-card-body">
                                    <div id="active-tests-panel" class="active-tests">
                                        <div class="ms-panel-content">
                                            <div class="ms-empty-message">No active tests</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="ms-grid-col ms-sm4">
                            <!-- Recent Test Cases Runs Card -->
                            <div class="ms-card recent-runs-card">
                                <div class="ms-card-header">
                                    <h4 class="ms-font-l">Recent Test Case Runs</h4>
                                </div>
                                <div class="ms-card-body" style="padding: 0; margin: 0;">
                                    <div class="table-container">
                                        <table class="ms-Table" id="recent-runs-table">
                                            <thead>
                                                <tr>
                                                    <th>Test ID</th>
                                                    <th>Status</th>
                                                    <th>Details</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody id="recent-runs-body">
                                                <!-- Recent runs will be populated here dynamically -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Floating Action Button for Quick Test Run -->
    <div id="floating-test-runner" class="floating-test-runner">
        <div id="quick-test-panel" class="quick-test-panel hidden">
            <div class="quick-test-header">
                <h4>Quick Test Run</h4>
                <button id="close-quick-test" class="close-btn">✕</button>
            </div>
            <div class="quick-test-body">
                <div class="quick-input-group">
                    <label for="quick-test-id">Test Case ID</label>
                    <input type="text" id="quick-test-id" placeholder="Enter test ID or select from search results" class="quick-input">
                </div>
                <div class="quick-settings-info">
                    <div class="settings-row">
                        <span class="settings-label">Project:</span>
                        <span id="quick-project-display">QA02</span>
                    </div>
                    <div class="settings-row">
                        <span class="settings-label">Shell Host:</span>
                        <span id="quick-shell-display">jps-qa10-app01</span>
                    </div>
                    <div class="settings-note">
                        <small>💡 Change settings in the main form above</small>
                    </div>
                </div>
                <div class="quick-actions">
                    <button id="quick-run-btn" class="quick-run-btn">
                        <span class="btn-icon">▶</span>
                        <span class="btn-text">Run Test</span>
                    </button>
                </div>
            </div>
        </div>
        <button id="fab-main" class="fab-main" title="Quick Test Run (Ctrl+Shift+R)">
            <span class="fab-icon">▶</span>
        </button>
    </div>

    <!-- Load authentication and API services -->
    <script src="auth-check.js"></script>
    <script src="config-auth.js"></script>
    <script src="environment-config.js"></script>
    <script src="services/api-service.js"></script>

    <!-- Load UI functionality -->
    <script src="js/floating-action-button.js"></script>
    <script src="js/test-search.js"></script>
    <script src="performance/simple-optimizations.js"></script>
    <script src="config.js"></script>

    <script>
        // Add service worker registration script
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('./service-worker.js').then(function(registration) {
                    console.log('Service worker registered:', registration);
                }).catch(function(registrationError) {
                    console.error('Service worker registration failed:', registrationError);
                });
            });
        } else {
            console.error('Service workers are not supported.');
        }
    </script>
    <!-- Load reports credentials helper for test details functionality -->
    <script src="../reports/reports.credentials.js"></script>
    <script src="../reports/reports.details-db.js"></script>
    <script src="../reports/reports.summary-parser.js"></script>
    <script src="../reports/reports.details-parser.js"></script>
    <script src="../reports/reports.ui-utils.js"></script>
    <script src="../reports/reports.details-external.js"></script>
    <script src="js/test-details-modal.js"></script>
</body>
</html>


