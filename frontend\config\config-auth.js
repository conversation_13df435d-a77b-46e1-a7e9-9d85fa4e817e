/**
 * config-auth.js
 *
 * Authentication handler for the Test Runner page that matches Dashboard functionality
 */

class ConfigAuth {
    constructor() {
        // Initialize unified auth client
        this.unifiedAuthClient = window.unifiedAuthClient;

        this.elements = {
            // Static login modal elements
            loginModal: document.getElementById('config-login-modal'),
            loginForm: document.getElementById('config-login-form'),
            loginButton: document.getElementById('login-button'),
            logoutButton: document.getElementById('logout-button'),
            usernameInput: document.getElementById('config-username'),
            passwordInput: document.getElementById('config-password'),
            loginStatus: document.getElementById('config-login-error'),
            userDisplay: document.getElementById('user-display'),
            environmentDisplay: document.getElementById('environment-display')
        };

        this.isAuthenticated = false;
        this.currentUser = null;
    }

    /**
     * Initialize authentication system
     */
    init() {
        this.bindEvents();
        window.addEventListener('auth:login', (e) => {
            this.isAuthenticated = true;
            this.currentUser = e.detail.user || { uid: e.detail.username };
            this.hideLoginModal();
            this.updateUI();
        });
        window.addEventListener('auth:logout', () => {
            this.isAuthenticated = false;
            this.currentUser = null;
            this.showLoginModal();
            this.updateUI();
        });
        window.addEventListener('auth:token-refresh', () => {
            console.log('Config: token refreshed');
        });
        this.checkInitialAuth();
        this.updateUI();
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        console.log('🔍 Config: Binding events...');
        console.log('🔍 Config: loginForm element:', this.elements.loginForm);

        if (this.elements.loginForm) {
            console.log('✅ Config: Adding submit event listener to login form');
            this.elements.loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        } else {
            console.warn('⚠️ Config: Login form element not found!');
        }

        if (this.elements.loginButton) {
            this.elements.loginButton.addEventListener('click', () => this.showLoginModal());
        }

        if (this.elements.logoutButton) {
            this.elements.logoutButton.addEventListener('click', () => this.handleLogout());
        }

        // Close modal when clicking outside (on overlay)
        if (this.elements.loginModal) {
            this.elements.loginModal.addEventListener('click', (e) => {
                // Only close if clicking on the overlay, not the modal content
                if (e.target.classList.contains('config-modal-overlay')) {
                    this.hideLoginModal();
                }
            });
        }
    }

    /**
     * Check for existing authentication
     */
    checkInitialAuth() {
        try {
            // Check if user is already authenticated via unified auth client
            if (this.unifiedAuthClient && this.unifiedAuthClient.isAuthenticated && this.unifiedAuthClient.currentUser) {
                this.isAuthenticated = true;
                this.currentUser = this.unifiedAuthClient.currentUser;
                this.hideLoginModal();

                console.log('✅ Config: Found existing JWT authentication for:', this.currentUser.uid);
                return true;
            } else {
                console.log('❌ Config: No valid JWT authentication found');
                this.showLoginModal();
                return false;
            }
        } catch (error) {
            console.error('Error checking JWT authentication:', error);
            this.showLoginModal();
            return false;
        }
    }

    /**
     * Show login modal - display static modal
     */
    showLoginModal() {
        console.log('🔑 Config: Showing login modal');

        if (this.elements.loginModal) {
            this.elements.loginModal.style.display = 'block';

            // Clear any previous error messages
            this.hideLoginError();

            // Focus on username input
            if (this.elements.usernameInput) {
                setTimeout(() => {
                    this.elements.usernameInput.focus();
                }, 100);
            }
        } else {
            console.warn('⚠️ Config: Login modal element not found');
        }
    }

    /**
     * Hide login modal
     */
    hideLoginModal() {
        console.log('🔒 Config: Hiding login modal');
        if (this.elements.loginModal) {
            this.elements.loginModal.style.display = 'none';
        }
    }



    /**
     * Handle login form submission
     */
    async handleLogin(event) {
        console.log('🔍 Config: handleLogin called');
        event.preventDefault();

        const username = this.elements.usernameInput?.value.trim();
        const password = this.elements.passwordInput?.value.trim();

        console.log(`🔍 Config: Login attempt - username: "${username}", password: "${password ? '[PROVIDED]' : '[EMPTY]'}"`);

        if (!username || !password) {
            console.warn('⚠️ Config: Missing username or password');
            this.showLoginError('Please enter both username and password');
            return;
        }

        try {
            console.log('🔐 Config: Attempting login for:', username);
            console.log('🔍 Config: unifiedAuthClient available:', !!this.unifiedAuthClient);

            // Use unified auth client for login
            const result = await this.unifiedAuthClient.login(username, password);
            console.log('🔍 Config: Raw login result:', result);

            if (result.success) {
                console.log('✅ Config: Login successful');

                // Update authentication state
                this.isAuthenticated = true;
                this.currentUser = result.user;

                // Hide login form
                this.hideLoginModal();

                // Update UI
                this.updateUI();

                // Clear form
                if (this.elements.usernameInput) this.elements.usernameInput.value = '';
                if (this.elements.passwordInput) this.elements.passwordInput.value = '';
                this.hideLoginError();

                // Trigger auth success event
                window.dispatchEvent(new CustomEvent('auth:login', {
                    detail: { user: result.user, username: username }
                }));

            } else {
                this.showLoginError(result.message || 'Login failed');
            }
        } catch (error) {
            console.error('❌ Config: Login error:', error);
            this.showLoginError('Login error: ' + error.message);
        }

        try {
            // Use unified auth client for login
            const result = await this.unifiedAuthClient.login(username, password);

            if (result.success) {
                // Update authentication state
                this.isAuthenticated = true;
                this.currentUser = result.user;

                // Hide modal and update UI
                this.hideLoginModal();
                this.updateUI();
                this.hideLoginError();

                // Clear form fields
                if (this.elements.usernameInput) this.elements.usernameInput.value = '';
                if (this.elements.passwordInput) this.elements.passwordInput.value = '';

                console.log('✅ Config: Login successful for:', username);
                console.log('🔍 Config: Login result:', result);

                // Update API service with credentials (for backward compatibility)
                if (window.apiService) {
                    console.log('🔍 Config: Setting API service credentials for:', username);
                    window.apiService.setCredentials(username, password);
                }

                // Verify session storage after login
                setTimeout(() => {
                    const stored = localStorage.getItem('smarttest_auth');
                    if (stored) {
                        const data = JSON.parse(stored);
                        console.log('🔍 Config: Session data after login:', JSON.stringify(data, null, 2));
                    } else {
                        console.warn('⚠️ Config: No session data found after login');
                    }
                }, 100);

                // Note: Removed authEventBus.dispatch call to prevent overwriting unified auth session data
                // The unified auth client already handles session storage properly

                // Test JWT token availability for debugging
                this.testJWTTokenAvailability();

            } else {
                console.warn('⚠️ Config: Login failed:', result.error || 'Unknown error');
                this.showLoginError(result.error || 'Login failed. Please check your credentials.');
            }

        } catch (error) {
            console.error('❌ Config: Login exception:', error);
            this.showLoginError('Login failed. Please try again.');
        }
    }

    /**
     * Handle logout
     */
    async handleLogout() {
        try {
            console.log('🔓 Config: Logout initiated');

            // Use unified auth client for logout
            if (this.unifiedAuthClient) {
                await this.unifiedAuthClient.logout();
            }

            // Update authentication state
            this.isAuthenticated = false;
            this.currentUser = null;

            // Clear API service credentials
            if (window.apiService && window.apiService.clearCredentials) {
                window.apiService.clearCredentials();
            }

            // Clear any cached data
            if (window.reportsCredentials && typeof window.reportsCredentials.clear === 'function') {
                window.reportsCredentials.clear();
            }

            // Update UI first
            this.updateUI();

            // Show login modal after a brief delay to ensure UI is updated
            setTimeout(() => {
                this.showLoginModal();
            }, 100);

            console.log('✅ Config: Logout successful');

            // Trigger auth events
            window.dispatchEvent(new CustomEvent('auth:logout', {
                detail: { module: 'Config' }
            }));

            if (window.authEventBus && typeof window.authEventBus.dispatch === 'function') {
                window.authEventBus.dispatch('logout');
            }

        } catch (error) {
            console.error('❌ Config: Logout error:', error);
            // Still show login modal even if logout had errors
            this.isAuthenticated = false;
            this.currentUser = null;
            this.updateUI();
            this.showLoginModal();
        }
    }

    /**
     * Update UI based on authentication state
     */
    updateUI() {
        // Format user display name (same as dashboard)
        const formatUserDisplay = (email) => {
            if (!email || !email.includes('@')) return email;
            const username = email.split('@')[0];
            return username.split('.').map(part => 
                part.charAt(0).toUpperCase() + part.slice(1)
            ).join(' ');
        };

        // Update user display element (right side of header)
        if (this.elements.userDisplay) {
            if (this.isAuthenticated) {
                const friendlyName = formatUserDisplay(this.currentUser?.uid);
                this.elements.userDisplay.textContent = `Logged in as: ${friendlyName}`;
            } else {
                this.elements.userDisplay.textContent = 'Not logged in';
            }
        }

        // Hide environment display element (not needed)
        if (this.elements.environmentDisplay) {
            this.elements.environmentDisplay.style.display = 'none';
        }

        // Update button visibility
        if (this.elements.loginButton) {
            this.elements.loginButton.style.display = this.isAuthenticated ? 'none' : 'inline-flex';
            console.log('🔘 Login button display:', this.elements.loginButton.style.display);
        }

        if (this.elements.logoutButton) {
            this.elements.logoutButton.style.display = this.isAuthenticated ? 'inline-flex' : 'none';
            console.log('🔘 Logout button display:', this.elements.logoutButton.style.display);
        }

        // Ensure login modal is hidden when authenticated
        if (this.isAuthenticated && this.elements.loginModal) {
            this.elements.loginModal.style.display = 'none';
            this.elements.loginModal.classList.remove('active');
            console.log('🔒 Login modal forcibly hidden in updateUI');
        }
    }

    /**
     * Show login error message
     */
    showLoginError(message) {
        console.log('⚠️ Config: Login error:', message);
        if (this.elements.loginStatus) {
            this.elements.loginStatus.textContent = message;
            this.elements.loginStatus.style.display = 'block';
        }
    }

    /**
     * Hide login error message
     */
    hideLoginError() {
        if (this.elements.loginStatus) {
            this.elements.loginStatus.style.display = 'none';
            this.elements.loginStatus.textContent = '';
        }
    }

    /**
     * Test JWT token availability for debugging
     */
    async testJWTTokenAvailability() {
        console.log('🔍 Testing JWT token availability...');

        // Check cookies
        const cookies = document.cookie.split(';').map(c => c.trim());
        const accessTokenCookie = cookies.find(c => c.startsWith('accessToken='));
        console.log('🍪 Access token cookie:', accessTokenCookie ? 'Present' : 'Missing');

        // Test API call to verify JWT authentication
        try {
            const response = await fetch('/csrf-token', {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                console.log('✅ JWT authentication test successful');
            } else {
                console.error('❌ JWT authentication test failed:', response.status, response.statusText);
            }
        } catch (error) {
            console.error('❌ JWT authentication test error:', error);
        }
    }

    /**
     * Get current JWT authentication status
     */
    getAuthStatus() {
        return {
            isAuthenticated: this.isAuthenticated,
            user: this.currentUser,
            credentials: this.isAuthenticated ? {
                uid: this.currentUser?.uid || sessionStorage.getItem('smarttest_uid'),
                isJWTAuth: true
            } : null
        };
    }
}

// Initialize authentication when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.configAuth = new ConfigAuth();
    window.configAuth.init();
});

// Export for use by other modules
window.ConfigAuth = ConfigAuth;
