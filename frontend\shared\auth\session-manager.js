/**
 * Frontend Session Manager
 * Bridges the gap between unified-auth-client and expected sessionManager interface
 */
class FrontendSessionManager {
    constructor() {
        this.sessionKey = 'smarttest_auth';
        this.initialized = false;
        this.authClient = null;
    }

    /**
     * Initialize with unified auth client
     */
    async initialize() {
        if (this.initialized) return true;

        // Wait for unified auth client
        let attempts = 0;
        while (attempts < 50 && !window.unifiedAuthClient) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
        }

        if (window.unifiedAuthClient) {
            this.authClient = window.unifiedAuthClient;
            this.initialized = true;
            console.log('✅ Frontend Session Manager initialized');
            return true;
        }

        console.error('❌ Frontend Session Manager: Unified auth client not available');
        return false;
    }

    /**
     * Get current session
     */
    getCurrentSession() {
        if (!this.initialized || !this.authClient) {
            return null;
        }

        if (this.authClient.isAuthenticated && this.authClient.currentUser) {
            return {
                sessionId: this.authClient.sessionId,
                user: this.authClient.currentUser,
                token: this.authClient.accessToken,
                expiresAt: new Date(this.authClient.sessionExpiry).toISOString()
            };
        }

        // Fallback to localStorage
        try {
            const stored = localStorage.getItem(this.sessionKey);
            if (stored) {
                const data = JSON.parse(stored);
                console.log('🔍 Session Manager: Checking stored data:', JSON.stringify(data, null, 2));
                console.log(`🔍 Session Manager: uid=${!!data.uid}, sessionId=${!!data.sessionId}, user=${!!data.user}, user.uid=${data.user?.uid}`);

                // Use user.uid if uid is not directly available
                const uid = data.uid || data.user?.uid;
                console.log(`🔍 Session Manager: Resolved uid="${uid}"`);

                if (uid && data.sessionId && data.sessionExpiry && data.user) {
                    // Check if not expired
                    const currentTime = Date.now();
                    const expiryTime = typeof data.sessionExpiry === 'string' ? new Date(data.sessionExpiry).getTime() : data.sessionExpiry;
                    console.log(`🔍 Session Manager: Time check - current=${currentTime}, expiry=${expiryTime}, valid=${currentTime < expiryTime}`);

                    if (currentTime < expiryTime) {
                        return {
                            sessionId: data.sessionId,
                            user: data.user,
                            token: data.accessToken || data.token || '',
                            expiresAt: new Date(expiryTime).toISOString()
                        };
                    } else {
                        console.log('🔍 Session Manager: Session expired, clearing');
                        localStorage.removeItem(this.sessionKey);
                    }
                } else {
                    console.log('🔍 Session Manager: Missing required fields');
                }
            }
        } catch (error) {
            console.warn('Failed to parse stored session:', error);
        }

        return null;
    }

    /**
     * Store session data
     */
    storeSession(sessionData) {
        try {
            const data = {
                uid: sessionData.user.uid,
                sessionId: sessionData.sessionId,
                sessionExpiry: new Date(sessionData.expiresAt).getTime(),
                user: sessionData.user,
                token: sessionData.token,
                permissions: sessionData.user.permissions || []
            };

            localStorage.setItem(this.sessionKey, JSON.stringify(data));
            
            // Update unified auth client if available
            if (this.authClient) {
                this.authClient.isAuthenticated = true;
                this.authClient.currentUser = sessionData.user;
                this.authClient.sessionId = sessionData.sessionId;
                this.authClient.sessionExpiry = new Date(sessionData.expiresAt).getTime();
                this.authClient.accessToken = sessionData.token;
            }

            console.log('✅ Session stored for:', sessionData.user.uid);
            
            // Notify other modules
            this.notifySessionUpdate(sessionData);
            return true;
        } catch (error) {
            console.error('❌ Failed to store session:', error);
            return false;
        }
    }

    /**
     * Clear session
     */
    clearSession() {
        try {
            localStorage.removeItem(this.sessionKey);
            
            // Clear unified auth client
            if (this.authClient) {
                this.authClient.isAuthenticated = false;
                this.authClient.currentUser = null;
                this.authClient.sessionId = null;
                this.authClient.sessionExpiry = null;
                this.authClient.accessToken = null;
            }

            console.log('🧹 Session cleared');
            this.notifySessionClear();
            return true;
        } catch (error) {
            console.error('❌ Failed to clear session:', error);
            return false;
        }
    }

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        const session = this.getCurrentSession();
        return session !== null;
    }

    /**
     * Notify modules of session updates
     */
    notifySessionUpdate(sessionData) {
        window.dispatchEvent(new CustomEvent('session:updated', {
            detail: sessionData
        }));

        // Also dispatch auth events for compatibility
        if (window.authEventBus) {
            window.authEventBus.dispatch('login', sessionData);
        }
    }

    /**
     * Notify modules of session clear
     */
    notifySessionClear() {
        window.dispatchEvent(new CustomEvent('session:cleared'));

        // Also dispatch auth events for compatibility
        if (window.authEventBus) {
            window.authEventBus.dispatch('logout', {});
        }
    }

    /**
     * Listen for cross-tab session changes
     */
    initCrossTabSync() {
        window.addEventListener('storage', (event) => {
            if (event.key === this.sessionKey) {
                if (event.newValue === null) {
                    // Session cleared in another tab
                    this.notifySessionClear();
                } else {
                    // Session updated in another tab
                    const session = this.getCurrentSession();
                    if (session) {
                        this.notifySessionUpdate(session);
                    }
                }
            }
        });
    }
}

// Create and initialize global instance immediately
const sessionManager = new FrontendSessionManager();

// Initialize when DOM is ready or immediately if already ready
function initializeSessionManager() {
    sessionManager.initialize().then(() => {
        sessionManager.initCrossTabSync();
        window.sessionManager = sessionManager;
        console.log('✅ Global session manager available');
    }).catch(error => {
        console.error('❌ Session manager initialization failed:', error);
        // Create a fallback session manager
        window.sessionManager = {
            getCurrentSession: () => null,
            storeSession: () => false,
            clearSession: () => false,
            isAuthenticated: () => false
        };
    });
}

if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeSessionManager);
} else {
    initializeSessionManager();
}

// Export for CommonJS compatibility
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FrontendSessionManager;
}
