Shared Credential Manager initialized
csrf-token-manager.js:73 🛡️ CSRFTokenManager initialized
dashboard/:25 ✅ Dashboard: CSRF token manager initialized
unified-api-service.js:100 Using dynamic base URL: http://localhost:3000
credential-manager.js:55 No valid credentials found in any storage location
unified-api-service.js:32 Unified API Service initialized for dashboard module with environment: Default (QA02)
unified-api-service.js:972 UnifiedApiService class and global apiService instance initialized
unified-api-service.js:1003 Unified API Service loaded with global compatibility mode
2external-api-service.js:56 External API Service initialized
external-api-service.js:507 Enhanced External API Service initialized
external-api-service.js:581 External API Services loaded and available globally
external-api-service.js:582 - externalApiService: true
external-api-service.js:583 - enhancedExternalApiService: true
test-suite-mapping.js:146 Frontend Test Suite Service loaded with 6 test suites
session-manager.js:28 ✅ Frontend Session Manager initialized
session-manager.js:217 ✅ Global session manager available
api-service.js:13 Creating Dashboard API Service with global UnifiedApiService
unified-api-service.js:100 Using dynamic base URL: http://localhost:3000
credential-manager.js:55 No valid credentials found in any storage location
unified-api-service.js:32 Unified API Service initialized for dashboard module with environment: Default (QA02)
unified-api-service.js:100 Using dynamic base URL: http://localhost:3000
api-service.js:163 Dashboard API Service (Unified) initialized successfully
dashboard-auth-init.js:7 🚀 Dashboard: Starting authentication initialization...
dashboard-auth-init.js:20 ✅ Dashboard: Unified auth client available
dashboard-auth-init.js:29 ℹ️ Dashboard: No active JWT session, user needs to login
dashboard-auth-init.js:40 Unified API Service is available, initializing dashboard listeners.
dashboard.js:396 Dashboard script loaded. Setting up 'apiservice-ready' listener.
dashboard-auth-init.js:142 ✅ Dashboard: Authentication initialization completed
dashboard/:1 [DOM] Input elements should have autocomplete attributes (suggested: "current-password"): (More info: https://goo.gl/9p2vKq) <input class=​"ms-TextField-field" type=​"password" id=​"password" placeholder=​"Enter your password" required>​…​</input>​
dashboard.js:310 Event apiservice-ready received. Starting main dashboard initialization.
dashboard.js:326 Creating new DashboardApiIntegration instance as it does not exist.
api-integration.js:35 DashboardApiIntegration created
api-integration.js:141 Successfully connected to API service
dashboard.js:330 Attempting to initialize DashboardApiIntegration...
credential-manager.js:55 No valid credentials found in any storage location
api-integration.js:2837 Showing login form modal
unified-api-service.js:980 Dispatched apiservice-ready event
dashboard.js:375 DashboardApiIntegration.initialize() reported failure. Dashboard may be incomplete.
initializeApiIntegrationAndData @ dashboard.js:375Understand this warning
dashboard.js:5 UI NOTIFICATION to be displayed [info]: Logging in - Please wait...
credential-manager.js:75 All credentials cleared
unified-auth-client.js:589 ✅ All stored authentication data cleared
unified-auth-client.js:230 ✅ Logout completed
unified-auth-client.js:137 🔍 Server login response: {
  "success": true,
  "user": {
    "uid": "<EMAIL>",
    "role": "admin",
    "name": "Iakov Volfkovich"
  },
  "permissions": [
    "read",
    "write",
    "delete",
    "manage_users",
    "view_logs"
  ],
  "expiresIn": 900,
  "tokenType": "Bearer"
}
unified-auth-client.js:146 🔍 JWT Session expiry set: 2025-08-11T11:21:46.531Z, expiresIn: 900s
unified-auth-client.js:147 🔍 Session ID set: sess_r4df5ek3mme70cfwi
unified-auth-client.js:148 🔍 Current user after login: {
  "uid": "<EMAIL>",
  "role": "admin",
  "name": "Iakov Volfkovich"
}
unified-auth-client.js:539 🔍 Storing auth state - currentUser: {
  "uid": "<EMAIL>",
  "role": "admin",
  "name": "Iakov Volfkovich"
}
unified-auth-client.js:540 🔍 Storing auth state - currentUser.uid: <EMAIL>
unified-auth-client.js:541 🔍 Storing auth state - sessionId: sess_r4df5ek3mme70cfwi
unified-auth-client.js:552 🔍 Data to be stored: {
  "uid": "<EMAIL>",
  "sessionId": "sess_r4df5ek3mme70cfwi",
  "sessionExpiry": 1754911306531,
  "user": {
    "uid": "<EMAIL>",
    "role": "admin",
    "name": "Iakov Volfkovich"
  },
  "permissions": [
    "read",
    "write",
    "delete",
    "manage_users",
    "view_logs"
  ]
}
unified-auth-client.js:553 🔍 Session expiry being stored: 1754911306531 (type: number)
unified-auth-client.js:554 🔍 Session expiry as ISO: 2025-08-11T11:21:46.531Z
unified-auth-client.js:556 ✅ JWT authentication state stored for: <EMAIL>
unified-auth-client.js:560 🔍 Verification - what was actually stored: {"uid":"<EMAIL>","sessionId":"sess_r4df5ek3mme70cfwi","sessionExpiry":1754911306531,"user":{"uid":"<EMAIL>","role":"admin","name":"Iakov Volfkovich"},"permissions":["read","write","delete","manage_users","view_logs"]}
unified-auth-client.js:509 ⏰ Automatic refresh scheduled in 780 seconds
unified-auth-client.js:429 ⏰ Periodic session validation started (every 300 seconds)
unified-auth-client.js:159 ✅ Login successful for: <EMAIL>
dashboard-auth-init.js:171 ✅ Dashboard: Login successful for user: <EMAIL>
dashboard-auth-init.js:178 Updated user display: <EMAIL> -> Iakov Volfkovich
dashboard.js:396 Dashboard script loaded. Setting up 'apiservice-ready' listener.
dashboard.js:5 UI NOTIFICATION to be displayed [success]: Login successful - Welcome to SmartTest Dashboard!
dashboard-auth-init.js:171 ✅ Dashboard: Login successful for user: <EMAIL>
dashboard-auth-init.js:178 Updated user display: <EMAIL> -> Iakov Volfkovich
dashboard.js:396 Dashboard script loaded. Setting up 'apiservice-ready' listener.
credential-manager.js:33 Credentials set for user: <EMAIL>, Storage success: true
dashboard.js:310 Event apiservice-ready received. Starting main dashboard initialization.
dashboard.js:330 Attempting to initialize DashboardApiIntegration...
api-integration.js:2846 Handling successful login for user: <EMAIL>
dashboard-auth-init.js:171 ✅ Dashboard: Login successful for user: <EMAIL>
dashboard-auth-init.js:178 Updated user display: <EMAIL> -> Iakov Volfkovich
dashboard.js:396 Dashboard script loaded. Setting up 'apiservice-ready' listener.
api-integration.js:1274 Status polling moved to unified recent runs polling system
api-integration.js:230 [DashboardApiIntegration] loadDashboardData: Starting to load dashboard data.
api-integration.js:232 Loading dashboard data...
api-integration.js:286 Loading test suites...
api-service.js:22 Frontend: Getting test suites with filters (no API call): Object
test-suite-mapping.js:72 Frontend: Filtering test suites with filters: Object
test-suite-mapping.js:78 Frontend: Returning all 6 test suites
api-service.js:28 Frontend: Returning 6 test suites from static mapping
api-integration.js:288 Test suites response: Array(6)
api-integration.js:307 Rendering test suites: Array(0)
api-integration.js:310 No test suites to render
api-integration.js:247 [DashboardApiIntegration] loadDashboardData: window.initialRecentRuns not found, fetching from API.
api-integration.js:248 Fetching recent runs from API...
api-integration.js:249 [DashboardApiIntegration] loadDashboardData: Calling apiService.getRecentRuns().
unified-api-service.js:314 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:319 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:251 [DashboardApiIntegration] loadDashboardData: Received recentRunsResponse from API: Array(20)
api-integration.js:263 [DashboardApiIntegration] loadDashboardData: recentRunsCache populated from API response: Array(20)
api-integration.js:1505 Updating dashboard counters from recent runs data
api-integration.js:1516 Found 3 runs for current user: <EMAIL>
api-integration.js:1536 Dashboard counters updated: Total=3, Passed=1, Failed=2, Running=0
api-integration.js:374 renderRecentTests called but is deprecated - Recent Test Executions UI has been removed
api-integration.js:2972 Smart merge: 0 raw tests merged to 0 unique tests
api-integration.js:1649 Deduplicated 0 raw tests to 0 processed tests
api-integration.js:1671 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2338 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1675 Current filter: undefined Current user: <EMAIL>
api-integration.js:1576 Applied 'undefined' filter: 0/0 tests included
api-integration.js:454 Starting poll request #1
api-integration.js:464 Using since_id=18456 for incremental polling (highestRecentTsnId=18457)
unified-api-service.js:314 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:319 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:2479 Setting up event listeners for dashboard components
api-integration.js:2533 Event listeners attached to active tests filter buttons.
api-integration.js:2427 [API Integration] Attempting to set up active tests filter event listeners...
api-integration.js:2434 [API Integration] Found 3 active tests filter buttons.
api-integration.js:2442 [API Integration] Set "All Tests" button as active by default
api-integration.js:2472 [API Integration] Successfully set up event listeners for active tests filter buttons.
api-integration.js:222 DashboardApiIntegration successfully initialized.
dashboard.js:335 DashboardApiIntegration initialized successfully. Setting up UI components.
dashboard.js:340 Main dashboard UI elements initialized
dashboard.js:676 Fetching test suites with filters: Object
api-service.js:22 Frontend: Getting test suites with filters (no API call): Object
test-suite-mapping.js:72 Frontend: Filtering test suites with filters: Object
test-suite-mapping.js:78 Frontend: Returning all 6 test suites
api-service.js:28 Frontend: Returning 6 test suites from static mapping
dashboard.js:640 Test suite selection handlers initialized
dashboard.js:561 updateRecentTestsTable called but is deprecated - Recent Test Executions UI has been removed
dashboard.js:242 Dashboard data saved to localStorage
dashboard-auth-init.js:171 ✅ Dashboard: Login successful for user: <EMAIL>
dashboard-auth-init.js:178 Updated user display: <EMAIL> -> Iakov Volfkovich
dashboard.js:396 Dashboard script loaded. Setting up 'apiservice-ready' listener.
api-integration.js:631 Test 18457 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18457 completion data: {passed_cases: 0, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18457 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18457 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:1604 Grace period (25s) expired for test 18457, removing from active tests, status: failed
api-integration.js:2972 Smart merge: 0 raw tests merged to 0 unique tests
api-integration.js:1649 Deduplicated 0 raw tests to 0 processed tests
api-integration.js:1671 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2338 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1675 Current filter: undefined Current user: <EMAIL>
api-integration.js:1576 Applied 'undefined' filter: 0/0 tests included
api-integration.js:1505 Updating dashboard counters from recent runs data
api-integration.js:1516 Found 3 runs for current user: <EMAIL>
api-integration.js:1536 Dashboard counters updated: Total=3, Passed=1, Failed=2, Running=0
api-integration.js:1604 Grace period (25s) expired for test 18457, removing from active tests, status: failed
api-integration.js:2972 Smart merge: 0 raw tests merged to 0 unique tests
api-integration.js:1649 Deduplicated 0 raw tests to 0 processed tests
api-integration.js:1671 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2338 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1675 Current filter: undefined Current user: <EMAIL>
api-integration.js:1576 Applied 'undefined' filter: 0/0 tests included
api-integration.js:558 Completed poll request #1, releasing lock
api-integration.js:2497 Run button clicked for suite: DEMO PE2.1 Heartbeat Test (323)
api-integration.js:2240 Loading: Running test suite 323...
api-service.js:109 Running test suite with ID: 323
unified-api-service.js:362 Making POST request to: http://localhost:3000/api/suite-runner
unified-api-service.js:408 Request parameters: {ts_id: '323'}
10Fetch finished loading: GET "<URL>".
api-integration.js:104 TSN ID is an object, attempting to extract ID: {success: true, testId: undefined, message: 'Test suite 323 started successfully with session ID 18458'}
api-integration.js:112 Extracted session ID from message: 18458
unified-api-service.js:411 Fetch finished loading: POST "http://localhost:3000/api/suite-runner".
postRequest @ unified-api-service.js:411
await in postRequest
apiService.runTestSuite @ api-service.js:113
runTestSuite @ api-integration.js:1075
(anonymous) @ api-integration.js:2498
api-integration.js:2273 Success: Test suite 323 started successfully. Run ID: 18458
api-integration.js:2972 Smart merge: 1 raw tests merged to 1 unique tests
api-integration.js:3109 Normalized test data for actual TSN_ID 18458 (original map key: 18458): {status: 'running', type: 'Test Suite', id: '323', ts_id: '323', startTime: Mon Aug 11 2025 14:07:01 GMT+0300 (Eastern European Summer Time), …}
api-integration.js:1649 Deduplicated 1 raw tests to 1 processed tests
api-integration.js:1671 Found 1 active tests that will always be shown with Stop buttons
api-integration.js:2338 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1675 Current filter: undefined Current user: <EMAIL>
api-integration.js:1572 Test 18458: user=<EMAIL>, filter=undefined, isMyTest=true, include=true
api-integration.js:1576 Applied 'undefined' filter: 1/1 tests included
api-integration.js:1740 Test 18458 - Using test data from: activeTests map, status: running
api-integration.js:1899 Test status for 18458: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1925 Test info for rendering: {testName: 'DEMO PE2.1 Heartbeat Test', testId: '323', tsn_id: '18458', isTestSuite: true, status: 'running', …}
api-integration.js:1980 Rendering card for session 18458: {testName: 'DEMO PE2.1 Heartbeat Test', testId: '323', sessionId: '18458', status: 'running', statusText: 'Running', …}
api-integration.js:2258 Loading complete
api-integration.js:454 Starting poll request #2
api-integration.js:464 Using since_id=18456 for incremental polling (highestRecentTsnId=18457)
unified-api-service.js:314 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:319 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:2240 Loading: Stopping test run 18458...
unified-api-service.js:362 Making POST request to: http://localhost:3000/api/stop-test
unified-api-service.js:408 Request parameters: {tsn_id: '18458'}
api-integration.js:1143 Error stopping test: Error: Failed to stop test
    at DashboardApiIntegration.stopTest (api-integration.js:1140:15)
stopTest @ api-integration.js:1143
await in stopTest
onclick @ dashboard/:1Understand this error
api-integration.js:2285 Error: Failed to stop test run 18458: Failed to stop test
showError @ api-integration.js:2285
stopTest @ api-integration.js:1144
await in stopTest
onclick @ dashboard/:1Understand this error
unified-api-service.js:411 Fetch finished loading: POST "http://localhost:3000/api/stop-test".
postRequest @ unified-api-service.js:411
await in postRequest
stopTest @ unified-api-service.js:763
stopTest @ api-integration.js:1122
onclick @ dashboard/:1
api-integration.js:2258 Loading complete
api-integration.js:631 Test 18458 has end_time, keeping active for grace period (status: Running)
api-integration.js:712 Test 18458 completion data: {passed_cases: 0, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:756 Test 18458 - No pass/fail data available (passed_cases=0, failed_cases=0, error="undefined")
api-integration.js:760 Test 18458 - Raw end time value: "2025-08-11 11:07:07" (type: string)
api-integration.js:775 Test 18458 - Timezone fix applied: "2025-08-11 11:07:07" -> corrected for 3h offset -> 2025-08-11T11:07:07.000Z
api-integration.js:782 Test 18458 - Parsed end time: 2025-08-11T11:07:07.000Z, Current time: 2025-08-11T11:07:09.103Z
api-integration.js:783 Test 18458 - Parsed end time (local): Mon Aug 11 2025 14:07:07 GMT+0300 (Eastern European Summer Time), Current time (local): Mon Aug 11 2025 14:07:09 GMT+0300 (Eastern European Summer Time)
api-integration.js:784 Test 18458 - End time timestamp: 1754910427000, Current time timestamp: 1754910429103
api-integration.js:790 Test 18458 - Recent completion (2.1s ago), keeping as running to allow test results to be written
api-integration.js:680 Skipping test 18457 as it was previously marked as completed
api-integration.js:1505 Updating dashboard counters from recent runs data
api-integration.js:1516 Found 4 runs for current user: <EMAIL>
api-integration.js:1536 Dashboard counters updated: Total=4, Passed=1, Failed=2, Running=1
api-integration.js:2930 Test 18458 is very fresh (8.1s old), activeTests data is completely authoritative
api-integration.js:2972 Smart merge: 2 raw tests merged to 1 unique tests
api-integration.js:3109 Normalized test data for actual TSN_ID 18458 (original map key: 18458): {status: 'running', type: 'Test Suite', id: '323', ts_id: 333, startTime: '2025-08-11 11:07:01', …}
api-integration.js:1649 Deduplicated 2 raw tests to 1 processed tests
api-integration.js:1671 Found 1 active tests that will always be shown with Stop buttons
api-integration.js:2338 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1675 Current filter: undefined Current user: <EMAIL>
api-integration.js:1572 Test 18458: user=<EMAIL>, filter=undefined, isMyTest=true, include=true
api-integration.js:1576 Applied 'undefined' filter: 1/1 tests included
api-integration.js:1740 Test 18458 - Using test data from: activeTests map, status: running
api-integration.js:1782 Test 18458 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1831 Test 18458 - Respecting timing-based running status (recent completion)
api-integration.js:1899 Test status for 18458: {statusClass: 'running', statusText: 'Running', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1925 Test info for rendering: {testName: 'PE2.1 Heartbeat Test', testId: 333, tsn_id: '18458', isTestSuite: true, status: 'running', …}
api-integration.js:1980 Rendering card for session 18458: {testName: 'PE2.1 Heartbeat Test', testId: 333, sessionId: '18458', status: 'running', statusText: 'Running', …}
api-integration.js:558 Completed poll request #2, releasing lock
api-integration.js:454 Starting poll request #3
api-integration.js:464 Using since_id=18457 for incremental polling (highestRecentTsnId=18458)
unified-api-service.js:314 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:319 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:631 Test 18458 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18458 completion data: {passed_cases: 2, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18458 - Detected as PASSED: passed_cases=2, passed=undefined
api-integration.js:805 Test 18458 has completed with status: passed (will be visible for 15-second grace period)
dashboard.js:5 UI NOTIFICATION to be displayed [success]: Suite Finished: PE2.1 Heartbeat Test - Status: Passed. Cases Passed: 2, Cases Failed: 0.
api-integration.js:1604 Grace period (25s) expired for test 18458, removing from active tests, status: passed
api-integration.js:2972 Smart merge: 1 raw tests merged to 1 unique tests
api-integration.js:3109 Normalized test data for actual TSN_ID 18458 (original map key: N/A): {tsn_id: '18458', tc_id: null, ts_id: 333, startTime: '2025-08-11 11:07:01', endTime: '2025-08-11 11:07:07', …}
api-integration.js:1649 Deduplicated 1 raw tests to 1 processed tests
api-integration.js:1671 Found 1 active tests that will always be shown with Stop buttons
api-integration.js:2338 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1675 Current filter: undefined Current user: <EMAIL>
api-integration.js:1572 Test 18458: user=<EMAIL>, filter=undefined, isMyTest=true, include=true
api-integration.js:1576 Applied 'undefined' filter: 1/1 tests included
api-integration.js:1740 Test 18458 - Using test data from: recentRunsCache, status: running
api-integration.js:1782 Test 18458 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1801 Test 18458 - Using completion detection status: passed
api-integration.js:1899 Test status for 18458: {statusClass: 'passed', statusText: 'Passed', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1925 Test info for rendering: {testName: 'PE2.1 Heartbeat Test', testId: 333, tsn_id: '18458', isTestSuite: true, status: 'running', …}
api-integration.js:1980 Rendering card for session 18458: {testName: 'PE2.1 Heartbeat Test', testId: 333, sessionId: '18458', status: 'passed', statusText: 'Passed', …}
api-integration.js:1464 Found 1 new completed tests to count in dashboard
api-integration.js:1478 Passed tests: 1, Failed tests: 0
api-integration.js:1489 Updated dashboard counters: {total: 5, successful: 2, failed: 2}
api-integration.js:1505 Updating dashboard counters from recent runs data
api-integration.js:1516 Found 4 runs for current user: <EMAIL>
api-integration.js:1536 Dashboard counters updated: Total=4, Passed=1, Failed=2, Running=1
api-integration.js:1604 Grace period (25s) expired for test 18458, removing from active tests, status: passed
api-integration.js:2972 Smart merge: 1 raw tests merged to 1 unique tests
api-integration.js:3109 Normalized test data for actual TSN_ID 18458 (original map key: N/A): {tsn_id: '18458', tc_id: null, ts_id: 333, startTime: '2025-08-11 11:07:01', endTime: '2025-08-11 11:07:07', …}
api-integration.js:1649 Deduplicated 1 raw tests to 1 processed tests
api-integration.js:1671 Found 1 active tests that will always be shown with Stop buttons
api-integration.js:2338 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1675 Current filter: undefined Current user: <EMAIL>
api-integration.js:1572 Test 18458: user=<EMAIL>, filter=undefined, isMyTest=true, include=true
api-integration.js:1576 Applied 'undefined' filter: 1/1 tests included
api-integration.js:1740 Test 18458 - Using test data from: recentRunsCache, status: running
api-integration.js:1782 Test 18458 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1801 Test 18458 - Using completion detection status: passed
api-integration.js:1899 Test status for 18458: {statusClass: 'passed', statusText: 'Passed', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1925 Test info for rendering: {testName: 'PE2.1 Heartbeat Test', testId: 333, tsn_id: '18458', isTestSuite: true, status: 'running', …}
api-integration.js:1980 Rendering card for session 18458: {testName: 'PE2.1 Heartbeat Test', testId: 333, sessionId: '18458', status: 'passed', statusText: 'Passed', …}
api-integration.js:558 Completed poll request #3, releasing lock
api-integration.js:454 Starting poll request #4
api-integration.js:464 Using since_id=18457 for incremental polling (highestRecentTsnId=18458)
unified-api-service.js:314 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:319 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:680 Skipping test 18458 as it was previously marked as completed
api-integration.js:1505 Updating dashboard counters from recent runs data
api-integration.js:1516 Found 4 runs for current user: <EMAIL>
api-integration.js:1536 Dashboard counters updated: Total=4, Passed=1, Failed=2, Running=1
api-integration.js:2972 Smart merge: 1 raw tests merged to 1 unique tests
api-integration.js:3109 Normalized test data for actual TSN_ID 18458 (original map key: N/A): {tsn_id: '18458', tc_id: null, ts_id: 333, startTime: '2025-08-11 11:07:01', endTime: '2025-08-11 11:07:07', …}
api-integration.js:1649 Deduplicated 1 raw tests to 1 processed tests
api-integration.js:1671 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2338 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1675 Current filter: undefined Current user: <EMAIL>
api-integration.js:1572 Test 18458: user=<EMAIL>, filter=undefined, isMyTest=true, include=true
api-integration.js:1576 Applied 'undefined' filter: 1/1 tests included
api-integration.js:1740 Test 18458 - Using test data from: recentRunsCache, status: running
api-integration.js:1782 Test 18458 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1827 Test 18458 - Test was previously completed, maintaining final status: completed (passed=0, failed=0)
api-integration.js:1899 Test status for 18458: {statusClass: 'completed', statusText: 'Completed', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1925 Test info for rendering: {testName: 'PE2.1 Heartbeat Test', testId: 333, tsn_id: '18458', isTestSuite: true, status: 'running', …}
api-integration.js:1980 Rendering card for session 18458: {testName: 'PE2.1 Heartbeat Test', testId: 333, sessionId: '18458', status: 'completed', statusText: 'Completed', …}
api-integration.js:558 Completed poll request #4, releasing lock
api-integration.js:454 Starting poll request #5
api-integration.js:464 Using since_id=18457 for incremental polling (highestRecentTsnId=18458)
unified-api-service.js:314 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:319 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:680 Skipping test 18458 as it was previously marked as completed
api-integration.js:1505 Updating dashboard counters from recent runs data
api-integration.js:1516 Found 4 runs for current user: <EMAIL>
api-integration.js:1536 Dashboard counters updated: Total=4, Passed=1, Failed=2, Running=1
api-integration.js:2972 Smart merge: 1 raw tests merged to 1 unique tests
api-integration.js:3109 Normalized test data for actual TSN_ID 18458 (original map key: N/A): {tsn_id: '18458', tc_id: null, ts_id: 333, startTime: '2025-08-11 11:07:01', endTime: '2025-08-11 11:07:07', …}
api-integration.js:1649 Deduplicated 1 raw tests to 1 processed tests
api-integration.js:1671 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2338 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1675 Current filter: undefined Current user: <EMAIL>
api-integration.js:1572 Test 18458: user=<EMAIL>, filter=undefined, isMyTest=true, include=true
api-integration.js:1576 Applied 'undefined' filter: 1/1 tests included
api-integration.js:1740 Test 18458 - Using test data from: recentRunsCache, status: running
api-integration.js:1782 Test 18458 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1827 Test 18458 - Test was previously completed, maintaining final status: completed (passed=0, failed=0)
api-integration.js:1899 Test status for 18458: {statusClass: 'completed', statusText: 'Completed', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1925 Test info for rendering: {testName: 'PE2.1 Heartbeat Test', testId: 333, tsn_id: '18458', isTestSuite: true, status: 'running', …}
api-integration.js:1980 Rendering card for session 18458: {testName: 'PE2.1 Heartbeat Test', testId: 333, sessionId: '18458', status: 'completed', statusText: 'Completed', …}
api-integration.js:558 Completed poll request #5, releasing lock
api-integration.js:454 Starting poll request #6
api-integration.js:464 Using since_id=18457 for incremental polling (highestRecentTsnId=18458)
unified-api-service.js:314 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:319 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:680 Skipping test 18458 as it was previously marked as completed
api-integration.js:1505 Updating dashboard counters from recent runs data
api-integration.js:1516 Found 4 runs for current user: <EMAIL>
api-integration.js:1536 Dashboard counters updated: Total=4, Passed=1, Failed=2, Running=1
api-integration.js:2972 Smart merge: 1 raw tests merged to 1 unique tests
api-integration.js:3109 Normalized test data for actual TSN_ID 18458 (original map key: N/A): {tsn_id: '18458', tc_id: null, ts_id: 333, startTime: '2025-08-11 11:07:01', endTime: '2025-08-11 11:07:07', …}
api-integration.js:1649 Deduplicated 1 raw tests to 1 processed tests
api-integration.js:1671 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2338 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1675 Current filter: undefined Current user: <EMAIL>
api-integration.js:1572 Test 18458: user=<EMAIL>, filter=undefined, isMyTest=true, include=true
api-integration.js:1576 Applied 'undefined' filter: 1/1 tests included
api-integration.js:1740 Test 18458 - Using test data from: recentRunsCache, status: running
api-integration.js:1782 Test 18458 status calculation - hasEndTime: true, testInfo.status: "running", passed: 0, failed: 0
api-integration.js:1827 Test 18458 - Test was previously completed, maintaining final status: completed (passed=0, failed=0)
api-integration.js:1899 Test status for 18458: {statusClass: 'completed', statusText: 'Completed', hasFailedTests: false, passed: 0, failed: 0, …}
api-integration.js:1925 Test info for rendering: {testName: 'PE2.1 Heartbeat Test', testId: 333, tsn_id: '18458', isTestSuite: true, status: 'running', …}
api-integration.js:1980 Rendering card for session 18458: {testName: 'PE2.1 Heartbeat Test', testId: 333, sessionId: '18458', status: 'completed', statusText: 'Completed', …}
api-integration.js:513 Performing full recent runs refresh
api-integration.js:517 Starting full refresh as part of request #6
unified-api-service.js:314 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:319 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:583 Skipping poll because previous request is still in progress
api-integration.js:536 Full refresh received 100 runs, updating recentRunsCache
api-integration.js:680 Skipping test 18458 as it was previously marked as completed
api-integration.js:680 Skipping test 18457 as it was previously marked as completed
api-integration.js:631 Test 18456 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18456 completion data: {passed_cases: 2, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
unified-api-service.js:333 Fetch finished loading: GET "http://localhost:3000/local/recent-runs?".
getRequest @ unified-api-service.js:333
getRecentRuns @ unified-api-service.js:877
pollRecentRuns @ api-integration.js:519
await in pollRecentRuns
(anonymous) @ api-integration.js:581
api-integration.js:730 Test 18456 - Detected as PASSED: passed_cases=2, passed=undefined
api-integration.js:805 Test 18456 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18455 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18455 completion data: {passed_cases: 2, failed_cases: 3, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18455 - Detected as FAILED: failed_cases=3, failed=undefined
api-integration.js:805 Test 18455 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18454 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18454 completion data: {passed_cases: 0, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18454 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18454 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18453 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18453 completion data: {passed_cases: 2, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18453 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18453 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18452 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18452 completion data: {passed_cases: 2, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18452 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18452 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18451 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18451 completion data: {passed_cases: 2, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18451 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18451 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18450 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18450 completion data: {passed_cases: 2, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18450 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18450 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18449 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18449 completion data: {passed_cases: 2, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18449 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18449 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18448 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18448 completion data: {passed_cases: 4, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18448 - Detected as PASSED: passed_cases=4, passed=undefined
api-integration.js:805 Test 18448 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18447 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18447 completion data: {passed_cases: 9, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18447 - Detected as PASSED: passed_cases=9, passed=undefined
api-integration.js:805 Test 18447 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18446 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18446 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18446 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18446 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18445 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18445 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18445 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18445 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18444 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18444 completion data: {passed_cases: 0, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18444 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18444 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18443 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18443 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18443 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18443 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18442 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18442 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18442 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18442 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18441 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18441 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18441 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18441 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18440 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18440 completion data: {passed_cases: 2, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18440 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18440 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18439 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18439 completion data: {passed_cases: 3, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18439 - Detected as PASSED: passed_cases=3, passed=undefined
api-integration.js:805 Test 18439 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18438 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18438 completion data: {passed_cases: 6, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18438 - Detected as PASSED: passed_cases=6, passed=undefined
api-integration.js:805 Test 18438 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18437 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18437 completion data: {passed_cases: 3, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18437 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18437 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18436 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18436 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18436 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18436 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18435 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18435 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18435 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18435 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18434 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18434 completion data: {passed_cases: 5, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18434 - Detected as PASSED: passed_cases=5, passed=undefined
api-integration.js:805 Test 18434 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18433 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18433 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18433 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18433 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18432 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18432 completion data: {passed_cases: 2, failed_cases: 2, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18432 - Detected as FAILED: failed_cases=2, failed=undefined
api-integration.js:805 Test 18432 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18431 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18431 completion data: {passed_cases: 3, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18431 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18431 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18430 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18430 completion data: {passed_cases: 3, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18430 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18430 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18429 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18429 completion data: {passed_cases: 3, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18429 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18429 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18428 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18428 completion data: {passed_cases: 3, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18428 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18428 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18427 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18427 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18427 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18427 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18426 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18426 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18426 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18426 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18425 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18425 completion data: {passed_cases: 3, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18425 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18425 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18424 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18424 completion data: {passed_cases: 3, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18424 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18424 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18423 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18423 completion data: {passed_cases: 3, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18423 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18423 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18422 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18422 completion data: {passed_cases: 3, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18422 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18422 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18421 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18421 completion data: {passed_cases: 3, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18421 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18421 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18420 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18420 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18420 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18420 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18419 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18419 completion data: {passed_cases: 0, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18419 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18419 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18418 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18418 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18418 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18418 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18417 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18417 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18417 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18417 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18416 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18416 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18416 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18416 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18415 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18415 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18415 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18415 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18414 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18414 completion data: {passed_cases: 0, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18414 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18414 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18413 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18413 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18413 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18413 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18412 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18412 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18412 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18412 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18411 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18411 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18411 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18411 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18410 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18410 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18410 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18410 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18409 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18409 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18409 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18409 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18408 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18408 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18408 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18408 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18407 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18407 completion data: {passed_cases: 0, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18407 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18407 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18406 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18406 completion data: {passed_cases: 0, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18406 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18406 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18405 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18405 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18405 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18405 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18404 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18404 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18404 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18404 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18403 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18403 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18403 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18403 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18402 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18402 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18402 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18402 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18401 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18401 completion data: {passed_cases: 3, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18401 - Detected as PASSED: passed_cases=3, passed=undefined
api-integration.js:805 Test 18401 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18400 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18400 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18400 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18400 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18399 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18399 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18399 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18399 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18398 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18398 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18398 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18398 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18397 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18397 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18397 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18397 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18396 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18396 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18396 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18396 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18395 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18395 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18395 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18395 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18394 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18394 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18394 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18394 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18393 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18393 completion data: {passed_cases: 4, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18393 - Detected as PASSED: passed_cases=4, passed=undefined
api-integration.js:805 Test 18393 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18392 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18392 completion data: {passed_cases: 4, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18392 - Detected as PASSED: passed_cases=4, passed=undefined
api-integration.js:805 Test 18392 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18391 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18391 completion data: {passed_cases: 4, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18391 - Detected as PASSED: passed_cases=4, passed=undefined
api-integration.js:805 Test 18391 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18390 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18390 completion data: {passed_cases: 4, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18390 - Detected as PASSED: passed_cases=4, passed=undefined
api-integration.js:805 Test 18390 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18389 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18389 completion data: {passed_cases: 4, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18389 - Detected as PASSED: passed_cases=4, passed=undefined
api-integration.js:805 Test 18389 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18388 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18388 completion data: {passed_cases: 4, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18388 - Detected as PASSED: passed_cases=4, passed=undefined
api-integration.js:805 Test 18388 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18387 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18387 completion data: {passed_cases: 4, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18387 - Detected as PASSED: passed_cases=4, passed=undefined
api-integration.js:805 Test 18387 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18386 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18386 completion data: {passed_cases: 4, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18386 - Detected as PASSED: passed_cases=4, passed=undefined
api-integration.js:805 Test 18386 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18385 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18385 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18385 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18385 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18384 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18384 completion data: {passed_cases: 0, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18384 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18384 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18383 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18383 completion data: {passed_cases: 0, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18383 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18383 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18382 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18382 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18382 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18382 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18381 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18381 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18381 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18381 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18380 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18380 completion data: {passed_cases: 0, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18380 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18380 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18379 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18379 completion data: {passed_cases: 0, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18379 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18379 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18378 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18378 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18378 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18378 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18377 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18377 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18377 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18377 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18376 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18376 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18376 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18376 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18375 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18375 completion data: {passed_cases: 5, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18375 - Detected as PASSED: passed_cases=5, passed=undefined
api-integration.js:805 Test 18375 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18374 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18374 completion data: {passed_cases: 0, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18374 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18374 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18373 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18373 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18373 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18373 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18372 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18372 completion data: {passed_cases: 0, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18372 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18372 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18371 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18371 completion data: {passed_cases: 0, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18371 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18371 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18370 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18370 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18370 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18370 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18369 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18369 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18369 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18369 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18368 has end_time, keeping active for grace period (status: Failed)
api-integration.js:712 Test 18368 completion data: {passed_cases: 0, failed_cases: 1, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:726 Test 18368 - Detected as FAILED: failed_cases=1, failed=undefined
api-integration.js:805 Test 18368 has completed with status: failed (will be visible for 15-second grace period)
api-integration.js:631 Test 18367 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18367 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18367 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18367 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18366 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18366 completion data: {passed_cases: 2, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18366 - Detected as PASSED: passed_cases=2, passed=undefined
api-integration.js:805 Test 18366 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18365 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18365 completion data: {passed_cases: 3, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18365 - Detected as PASSED: passed_cases=3, passed=undefined
api-integration.js:805 Test 18365 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18364 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18364 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18364 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18364 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18363 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18363 completion data: {passed_cases: 3, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18363 - Detected as PASSED: passed_cases=3, passed=undefined
api-integration.js:805 Test 18363 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18362 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18362 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18362 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18362 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18361 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18361 completion data: {passed_cases: 2, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18361 - Detected as PASSED: passed_cases=2, passed=undefined
api-integration.js:805 Test 18361 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18360 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18360 completion data: {passed_cases: 1, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18360 - Detected as PASSED: passed_cases=1, passed=undefined
api-integration.js:805 Test 18360 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:631 Test 18359 has end_time, keeping active for grace period (status: Passed)
api-integration.js:712 Test 18359 completion data: {passed_cases: 3, failed_cases: 0, passed: undefined, failed: undefined, error: undefined, …}
api-integration.js:730 Test 18359 - Detected as PASSED: passed_cases=3, passed=undefined
api-integration.js:805 Test 18359 has completed with status: passed (will be visible for 15-second grace period)
api-integration.js:1604 Grace period (25s) expired for test 18456, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18455, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18454, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18453, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18452, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18451, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18450, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18449, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18448, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18447, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18446, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18445, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18444, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18443, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18442, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18441, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18440, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18439, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18438, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18437, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18436, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18435, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18434, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18433, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18432, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18431, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18430, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18429, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18428, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18427, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18426, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18425, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18424, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18423, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18422, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18421, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18420, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18419, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18418, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18417, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18416, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18415, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18414, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18413, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18412, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18411, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18410, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18409, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18408, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18407, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18406, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18405, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18404, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18403, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18402, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18401, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18400, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18399, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18398, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18397, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18396, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18395, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18394, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18393, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18392, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18391, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18390, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18389, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18388, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18387, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18386, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18385, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18384, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18383, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18382, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18381, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18380, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18379, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18378, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18377, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18376, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18375, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18374, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18373, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18372, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18371, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18370, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18369, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18368, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18367, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18366, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18365, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18364, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18363, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18362, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18361, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18360, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18359, removing from active tests, status: passed
api-integration.js:2972 Smart merge: 0 raw tests merged to 0 unique tests
api-integration.js:1649 Deduplicated 0 raw tests to 0 processed tests
api-integration.js:1671 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2338 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1675 Current filter: undefined Current user: <EMAIL>
api-integration.js:1576 Applied 'undefined' filter: 0/0 tests included
api-integration.js:1505 Updating dashboard counters from recent runs data
api-integration.js:1516 Found 9 runs for current user: <EMAIL>
api-integration.js:1536 Dashboard counters updated: Total=9, Passed=7, Failed=2, Running=0
api-integration.js:1604 Grace period (25s) expired for test 18456, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18455, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18454, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18453, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18452, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18451, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18450, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18449, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18448, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18447, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18446, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18445, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18444, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18443, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18442, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18441, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18440, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18439, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18438, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18437, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18436, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18435, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18434, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18433, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18432, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18431, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18430, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18429, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18428, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18427, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18426, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18425, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18424, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18423, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18422, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18421, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18420, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18419, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18418, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18417, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18416, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18415, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18414, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18413, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18412, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18411, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18410, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18409, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18408, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18407, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18406, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18405, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18404, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18403, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18402, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18401, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18400, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18399, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18398, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18397, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18396, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18395, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18394, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18393, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18392, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18391, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18390, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18389, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18388, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18387, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18386, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18385, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18384, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18383, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18382, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18381, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18380, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18379, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18378, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18377, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18376, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18375, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18374, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18373, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18372, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18371, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18370, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18369, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18368, removing from active tests, status: failed
api-integration.js:1604 Grace period (25s) expired for test 18367, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18366, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18365, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18364, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18363, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18362, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18361, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18360, removing from active tests, status: passed
api-integration.js:1604 Grace period (25s) expired for test 18359, removing from active tests, status: passed
api-integration.js:2972 Smart merge: 0 raw tests merged to 0 unique tests
api-integration.js:1649 Deduplicated 0 raw tests to 0 processed tests
api-integration.js:1671 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2338 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1675 Current filter: undefined Current user: <EMAIL>
api-integration.js:1576 Applied 'undefined' filter: 0/0 tests included
api-integration.js:558 Completed poll request #6, releasing lock
api-integration.js:454 Starting poll request #7
api-integration.js:464 Using since_id=18457 for incremental polling (highestRecentTsnId=18458)
unified-api-service.js:314 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:319 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:680 Skipping test 18458 as it was previously marked as completed
api-integration.js:1505 Updating dashboard counters from recent runs data
api-integration.js:1516 Found 4 runs for current user: <EMAIL>
api-integration.js:1536 Dashboard counters updated: Total=4, Passed=2, Failed=2, Running=0
api-integration.js:2972 Smart merge: 0 raw tests merged to 0 unique tests
api-integration.js:1649 Deduplicated 0 raw tests to 0 processed tests
api-integration.js:1671 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2338 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1675 Current filter: undefined Current user: <EMAIL>
api-integration.js:1576 Applied 'undefined' filter: 0/0 tests included
api-integration.js:558 Completed poll request #7, releasing lock
api-integration.js:454 Starting poll request #8
api-integration.js:464 Using since_id=18457 for incremental polling (highestRecentTsnId=18458)
unified-api-service.js:314 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:319 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:680 Skipping test 18458 as it was previously marked as completed
api-integration.js:1505 Updating dashboard counters from recent runs data
api-integration.js:1516 Found 4 runs for current user: <EMAIL>
api-integration.js:1536 Dashboard counters updated: Total=4, Passed=2, Failed=2, Running=0
api-integration.js:2972 Smart merge: 0 raw tests merged to 0 unique tests
api-integration.js:1649 Deduplicated 0 raw tests to 0 processed tests
api-integration.js:1671 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2338 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1675 Current filter: undefined Current user: <EMAIL>
api-integration.js:1576 Applied 'undefined' filter: 0/0 tests included
api-integration.js:558 Completed poll request #8, releasing lock
api-integration.js:454 Starting poll request #9
api-integration.js:464 Using since_id=18457 for incremental polling (highestRecentTsnId=18458)
unified-api-service.js:314 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:319 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:680 Skipping test 18458 as it was previously marked as completed
api-integration.js:1505 Updating dashboard counters from recent runs data
api-integration.js:1516 Found 4 runs for current user: <EMAIL>
api-integration.js:1536 Dashboard counters updated: Total=4, Passed=2, Failed=2, Running=0
api-integration.js:2972 Smart merge: 0 raw tests merged to 0 unique tests
api-integration.js:1649 Deduplicated 0 raw tests to 0 processed tests
api-integration.js:1671 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2338 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1675 Current filter: undefined Current user: <EMAIL>
api-integration.js:1576 Applied 'undefined' filter: 0/0 tests included
api-integration.js:558 Completed poll request #9, releasing lock
api-integration.js:454 Starting poll request #10
api-integration.js:464 Using since_id=18457 for incremental polling (highestRecentTsnId=18458)
unified-api-service.js:314 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:319 Making GET request to: http://localhost:3000/local/recent-runs
api-integration.js:680 Skipping test 18458 as it was previously marked as completed
api-integration.js:1505 Updating dashboard counters from recent runs data
api-integration.js:1516 Found 4 runs for current user: <EMAIL>
api-integration.js:1536 Dashboard counters updated: Total=4, Passed=2, Failed=2, Running=0
api-integration.js:2972 Smart merge: 0 raw tests merged to 0 unique tests
api-integration.js:1649 Deduplicated 0 raw tests to 0 processed tests
api-integration.js:1671 Found 0 active tests that will always be shown with Stop buttons
api-integration.js:2338 [getCurrentUser] Got user from apiService credentials: <EMAIL>
api-integration.js:1675 Current filter: undefined Current user: <EMAIL>
api-integration.js:1576 Applied 'undefined' filter: 0/0 tests included
api-integration.js:558 Completed poll request #10, releasing lock
api-integration.js:454 Starting poll request #11
api-integration.js:464 Using since_id=18457 for incremental polling (highestRecentTsnId=18458)
unified-api-service.js:314 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:319 Making GET request to: http://localhost:3000/local/recent-runs