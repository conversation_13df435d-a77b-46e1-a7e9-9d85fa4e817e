hared Credential Manager initialized
18:37:35.273 unified-auth-client.js:46 🔍 Stored session data: {
  "username": "i<PERSON><PERSON>.<EMAIL>",
  "user": {
    "uid": "<EMAIL>",
    "role": "admin",
    "name": "<PERSON><PERSON><PERSON>"
  }
}
18:37:35.274 unified-auth-client.js:55 🔍 Session check: uid=false, sessionId=false, expiryTime=undefined, user=true
18:37:35.274 unified-auth-client.js:56 🔍 Extracted values: uid="undefined", sessionId="undefined", user.uid="<EMAIL>", actualUid="<EMAIL>"
18:37:35.274 unified-auth-client.js:79 Error initializing JWT auth from storage: RangeError: Invalid time value
    at Date.toISOString (<anonymous>)
    at UnifiedAuthClient.initializeFromStorage (unified-auth-client.js:57:152)
    at new UnifiedAuthClient (unified-auth-client.js:35:10)
    at unified-auth-client.js:657:27
initializeFromStorage @ unified-auth-client.js:79
UnifiedAuthClient @ unified-auth-client.js:35
(anonymous) @ unified-auth-client.js:657
18:37:35.275 unified-auth-client.js:548 ✅ All stored authentication data cleared
18:37:35.276 csrf-token-manager.js:73 🛡️ CSRFTokenManager initialized
18:37:35.276 index.html:27 ✅ Config: CSRF token manager initialized
18:37:35.277 unified-api-service.js:100 Using dynamic base URL: http://localhost:3000
18:37:35.278 credential-manager.js:55 No valid credentials found in any storage location
18:37:35.278 unified-api-service.js:32 Unified API Service initialized for config module with environment: Default (QA02)
18:37:35.278 unified-api-service.js:972 UnifiedApiService class and global apiService instance initialized
18:37:35.278 unified-api-service.js:1003 Unified API Service loaded with global compatibility mode
18:37:35.279 external-api-service.js:56 External API Service initialized
18:37:35.279 external-api-service.js:56 External API Service initialized
18:37:35.279 external-api-service.js:507 Enhanced External API Service initialized
18:37:35.279 external-api-service.js:581 External API Services loaded and available globally
18:37:35.279 external-api-service.js:582 - externalApiService: true
18:37:35.279 external-api-service.js:583 - enhancedExternalApiService: true
18:37:35.281 auth-check.js:6 === CONFIG AUTH CHECK SCRIPT (Updated) ===
18:37:35.282 api-service.js:8 === API SERVICE SCRIPT STARTING ===
18:37:35.282 api-service.js:11 API Service initialization - existing window.apiService: UnifiedApiService {envConfig: null, moduleContext: 'config', baseUrls: {…}, contentTypes: {…}, endpoints: {…}, …}
18:37:35.282 api-service.js:12 API Service initialization - existing window.UnifiedApiService: class UnifiedApiService {
  constructor() {
    // Initialize environment configuration if available
    this.envConfig = this.loadEnvironmentConfig();

    // Initialize configuration based on module…
18:37:35.284 api-service.js:17 Setting up apiService using global UnifiedApiService
18:37:35.284 unified-api-service.js:100 Using dynamic base URL: http://localhost:3000
18:37:35.284 api-service.js:33 apiService is now configured and available globally
18:37:35.284 api-service.js:34 apiService methods: (8) ['envConfig', 'moduleContext', 'baseUrls', 'contentTypes', 'endpoints', 'defaultTestParams', 'credentials', 'csrfTokenManager']
18:37:35.284 api-service.js:35 searchTestCases available: true
18:37:35.284 api-service.js:58 API Service setup complete - window.apiService should now be available
18:37:35.301 test-search.js:7 Loading test-search.js script...
18:37:35.308 session-manager.js:28 ✅ Frontend Session Manager initialized
18:37:35.309 session-manager.js:217 ✅ Global session manager available
18:37:35.309 auth-check.js:14 🚀 Config: Initializing authentication system
18:37:35.309 auth-check.js:47 🔍 Config: Checking dependencies... sessionManager=true, UniversalAuthCheck=true, waited=0ms
18:37:35.309 auth-check.js:50 ✅ Config: Dependencies loaded
18:37:35.309 universal-auth-check.js:16 🔍 Config: Starting authentication check
18:37:35.309 universal-auth-check.js:20 🔍 Config: Auth check attempt 1/5
18:37:35.309 universal-auth-check.js:33 ⏳ Config: Authentication not ready, retrying in 1000ms
18:37:35.309 config-auth.js:92 ❌ Config: No valid JWT authentication found
18:37:35.312 config-auth.js:246 🔘 Login button display: inline-flex
18:37:35.312 config-auth.js:251 🔘 Logout button display: none
18:37:35.312 floating-action-button.js:313 Floating Action Button initialized
18:37:35.312 test-search.js:32 DOM loaded in test-search.js
18:37:35.312 test-search.js:36 🔍 Checking if API service is available...
18:37:35.312 test-search.js:37 🔍 window.apiService exists: true
18:37:35.312 test-search.js:39 🔍 window.apiService methods: (8) ['envConfig', 'moduleContext', 'baseUrls', 'contentTypes', 'endpoints', 'defaultTestParams', 'credentials', 'csrfTokenManager']
18:37:35.313 test-search.js:40 🔍 searchTestCases type: function
18:37:35.313 test-search.js:44 ✅ API service is available with searchTestCases method!
18:37:35.313 test-search.js:72 🚀 Initializing search form...
18:37:35.313 test-search.js:77 🔍 searchForm element: <form id=​"test-search-form">​…​</form>​
18:37:35.313 test-search.js:78 🔍 searchButton element: <button type=​"button" class=​"ms-Button ms-Button--primary" id=​"search-test-cases-btn">​…​</button>​
18:37:35.313 test-search.js:117 ✅ Search form initialized with event listeners - COMPLETE!
18:37:35.313 test-search.js:118 🔍 searchFormInitialized flag set to: true
18:37:35.313 simple-optimizations.js:186 🚀 Initializing simple performance optimizations...
18:37:35.313 simple-optimizations.js:237 ✅ API service optimized with simple request manager
18:37:35.313 simple-optimizations.js:200 ✅ Simple performance optimizations initialized
18:37:35.314 config.js:108 🚀 initCustomTestRunner called
18:37:35.314 config.js:198 No JWT authentication found, checking legacy credentials...
18:37:35.314 config.js:214 No stored credentials found - authentication required
18:37:35.314 simple-optimizations.js:186 🚀 Initializing simple performance optimizations...
18:37:35.314 simple-optimizations.js:200 ✅ Simple performance optimizations initialized
18:37:35.314 config.js:92 ✅ Simple performance optimizations initialized
18:37:35.314 config.js:124 🔍 Checking API service availability...
18:37:35.314 config.js:125 window.apiService: UnifiedApiService {envConfig: null, moduleContext: 'config', baseUrls: {…}, contentTypes: {…}, endpoints: {…}, …}
18:37:35.314 config.js:128 ✅ API service already available, setting credentials and starting polling...
18:37:35.316 index.html:1 [DOM] Input elements should have autocomplete attributes (suggested: "current-password"): (More info: https://goo.gl/9p2vKq) <input class=​"ms-TextField-field" type=​"password" id=​"password" placeholder=​"Enter your password" required>​
18:37:35.429 test-search.js:54 Received apiservice-ready event, initializing search form
18:37:35.429 test-search.js:68 ⚠️ Search form already initialized, skipping...
18:37:35.429 unified-api-service.js:980 Dispatched apiservice-ready event
18:37:35.445 index.html:451 Service worker registered: ServiceWorkerRegistration {installing: null, waiting: null, active: ServiceWorker, navigationPreload: NavigationPreloadManager, scope: 'http://localhost:3000/config/', …}
18:37:35.818 config.js:436 Starting polling...
18:37:35.819 config.js:484 Starting simple optimized polling...
18:37:35.819 simple-optimizations.js:116 Subscribed config-recent-runs to recentRuns
18:37:35.819 simple-optimizations.js:116 Subscribed config-active-tests to activeTests
18:37:35.819 config.js:500 ✅ Subscribed to simple coordinated polling system
18:37:36.309 unified-api-service.js:314 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
18:37:36.310 unified-api-service.js:319 Making GET request to: http://localhost:3000/local/recent-runs
18:37:36.310 simple-optimizations.js:140 Started polling recentRuns every 8000ms
18:37:36.310 unified-api-service.js:314 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
18:37:36.310 unified-api-service.js:319 Making GET request to: http://localhost:3000/local/recent-runs
18:37:36.310 simple-optimizations.js:140 Started polling activeTests every 2000ms
18:37:36.310 simple-optimizations.js:292 ✅ Coordinated polling setup complete
18:37:36.310 universal-auth-check.js:20 🔍 Config: Auth check attempt 2/5
18:37:36.311 universal-auth-check.js:33 ⏳ Config: Authentication not ready, retrying in 1000ms
18:37:37.318 universal-auth-check.js:20 🔍 Config: Auth check attempt 3/5
18:37:37.318 universal-auth-check.js:33 ⏳ Config: Authentication not ready, retrying in 1000ms
18:37:38.312 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:37:38.328 universal-auth-check.js:20 🔍 Config: Auth check attempt 4/5
18:37:38.328 universal-auth-check.js:33 ⏳ Config: Authentication not ready, retrying in 1000ms
18:37:39.328 universal-auth-check.js:20 🔍 Config: Auth check attempt 5/5
18:37:39.329 universal-auth-check.js:29 ❌ Config: Authentication failed after 5 attempts
18:37:39.330 universal-auth-check.js:216 🔑 Config: Showing inline login
18:37:39.331 auth-check.js:29 ❌ Config: Authentication failed, showing login options
18:37:39.436 index.html:1 [DOM] Input elements should have autocomplete attributes (suggested: "current-password"): (More info: https://goo.gl/9p2vKq) <input type=​"password" id=​"inline-password" placeholder=​"Password" required>​
18:37:40.321 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:37:42.325 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:37:43.494 credential-manager.js:75 All credentials cleared
18:37:43.495 unified-auth-client.js:548 ✅ All stored authentication data cleared
18:37:43.495 unified-auth-client.js:201 ✅ Logout completed
18:37:43.496 config-auth.js:246 🔘 Login button display: inline-flex
18:37:43.496 config-auth.js:251 🔘 Logout button display: none
18:37:43.523 unified-auth-client.js:108 🔍 Server login response: {
  "success": true,
  "user": {
    "uid": "<EMAIL>",
    "role": "admin",
    "name": "Iakov Volfkovich"
  },
  "permissions": [
    "read",
    "write",
    "delete",
    "manage_users",
    "view_logs"
  ],
  "expiresIn": 900,
  "tokenType": "Bearer"
}
18:37:43.523 unified-auth-client.js:117 🔍 JWT Session expiry set: 2025-08-06T15:52:43.523Z, expiresIn: 900s
18:37:43.523 unified-auth-client.js:118 🔍 Session ID set: sess_judnyfbtbme04tmjn
18:37:43.523 unified-auth-client.js:119 🔍 Current user after login: {
  "uid": "<EMAIL>",
  "role": "admin",
  "name": "Iakov Volfkovich"
}
18:37:43.523 unified-auth-client.js:500 🔍 Storing auth state - currentUser: {
  "uid": "<EMAIL>",
  "role": "admin",
  "name": "Iakov Volfkovich"
}
18:37:43.523 unified-auth-client.js:501 🔍 Storing auth state - currentUser.uid: <EMAIL>
18:37:43.523 unified-auth-client.js:502 🔍 Storing auth state - sessionId: sess_judnyfbtbme04tmjn
18:37:43.523 unified-auth-client.js:513 🔍 Data to be stored: {
  "uid": "<EMAIL>",
  "sessionId": "sess_judnyfbtbme04tmjn",
  "sessionExpiry": 1754495563523,
  "user": {
    "uid": "<EMAIL>",
    "role": "admin",
    "name": "Iakov Volfkovich"
  },
  "permissions": [
    "read",
    "write",
    "delete",
    "manage_users",
    "view_logs"
  ]
}
18:37:43.524 unified-auth-client.js:515 ✅ JWT authentication state stored for: <EMAIL>
18:37:43.524 unified-auth-client.js:519 🔍 Verification - what was actually stored: {"uid":"<EMAIL>","sessionId":"sess_judnyfbtbme04tmjn","sessionExpiry":1754495563523,"user":{"uid":"<EMAIL>","role":"admin","name":"Iakov Volfkovich"},"permissions":["read","write","delete","manage_users","view_logs"]}
18:37:43.524 unified-auth-client.js:470 ⏰ Automatic refresh scheduled in 780 seconds
18:37:43.524 unified-auth-client.js:390 ⏰ Periodic session validation started (every 300 seconds)
18:37:43.524 unified-auth-client.js:130 ✅ Login successful for: <EMAIL>
18:37:43.524 config-auth.js:127 🔒 Login modal hidden
18:37:43.524 config-auth.js:246 🔘 Login button display: none
18:37:43.524 config-auth.js:251 🔘 Logout button display: inline-flex
18:37:43.524 config-auth.js:258 🔒 Login modal forcibly hidden in updateUI
18:37:43.525 config-auth.js:127 🔒 Login modal hidden
18:37:43.526 config-auth.js:246 🔘 Login button display: none
18:37:43.526 config-auth.js:251 🔘 Logout button display: inline-flex
18:37:43.526 config-auth.js:258 🔒 Login modal forcibly hidden in updateUI
18:37:43.526 config-auth.js:161 Login successful for: <EMAIL>
18:37:43.526 credential-manager.js:33 Credentials set for user: <EMAIL>, Storage success: true
18:37:43.526 config-auth.js:127 🔒 Login modal hidden
18:37:43.526 config-auth.js:246 🔘 Login button display: none
18:37:43.526 config-auth.js:251 🔘 Logout button display: inline-flex
18:37:43.526 config-auth.js:258 🔒 Login modal forcibly hidden in updateUI
18:37:43.526 config-auth.js:285 🔍 Testing JWT token availability...
18:37:43.527 config-auth.js:290 🍪 Access token cookie: Missing
18:37:43.532 config-auth.js:303 ✅ JWT authentication test successful
18:37:44.323 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":50,"type":"single_case"}
18:37:44.324 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:37:46.319 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:37:48.312 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:37:50.316 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:37:52.321 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":50,"type":"single_case"}
18:37:52.322 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:37:54.314 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:37:55.390 simple-optimizations.js:47 Request completed: recentRuns_{"limit":50,"type":"single_case"}
18:37:55.390 config.js:488 Received 50 recent runs update
18:37:55.390 config.js:597 processRecentRunsData called with 50 runs
18:37:55.390 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:37:55.390 config.js:880 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
18:37:55.391 config.js:881 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
18:37:55.391 config.js:899 Rendering 50 recent runs
18:37:55.393 config.js:488 Received 50 recent runs update
18:37:55.393 config.js:597 processRecentRunsData called with 50 runs
18:37:55.393 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:37:55.393 config.js:880 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
18:37:55.393 config.js:881 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
18:37:55.393 config.js:899 Rendering 50 recent runs
18:37:55.395 config.js:488 Received 50 recent runs update
18:37:55.395 config.js:597 processRecentRunsData called with 50 runs
18:37:55.395 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:37:55.395 config.js:880 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
18:37:55.395 config.js:881 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
18:37:55.395 config.js:899 Rendering 50 recent runs
18:37:56.317 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:37:57.055 simple-optimizations.js:47 Request completed: recentRuns_{"limit":100,"type":"single_case"}
18:37:57.055 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:37:57.055 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:37:57.055 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:37:57.055 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:37:57.056 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:37:57.056 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:37:57.056 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:37:57.056 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:37:57.056 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:37:57.056 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:37:57.056 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:37:57.057 config.js:496 Received 0 active tests update
18:37:57.057 config.js:550 Updating active tests display with 0 tests
18:37:57.057 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:37:57.057 config.js:496 Received 0 active tests update
18:37:57.057 config.js:550 Updating active tests display with 0 tests
18:37:57.058 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:37:57.058 config.js:496 Received 0 active tests update
18:37:57.058 config.js:550 Updating active tests display with 0 tests
18:37:57.058 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:37:57.058 config.js:496 Received 0 active tests update
18:37:57.059 config.js:550 Updating active tests display with 0 tests
18:37:57.059 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:37:57.059 config.js:496 Received 0 active tests update
18:37:57.059 config.js:550 Updating active tests display with 0 tests
18:37:57.059 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:37:57.059 config.js:496 Received 0 active tests update
18:37:57.059 config.js:550 Updating active tests display with 0 tests
18:37:57.060 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:37:57.060 config.js:496 Received 0 active tests update
18:37:57.060 config.js:550 Updating active tests display with 0 tests
18:37:57.060 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:37:57.060 config.js:496 Received 0 active tests update
18:37:57.060 config.js:550 Updating active tests display with 0 tests
18:37:57.060 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:37:57.061 config.js:496 Received 0 active tests update
18:37:57.061 config.js:550 Updating active tests display with 0 tests
18:37:57.061 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:37:57.061 config.js:496 Received 0 active tests update
18:37:57.061 config.js:550 Updating active tests display with 0 tests
18:37:57.061 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:37:57.061 config.js:496 Received 0 active tests update
18:37:57.061 config.js:550 Updating active tests display with 0 tests
18:37:57.061 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:37:58.322 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
18:37:58.322 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:37:58.322 config.js:496 Received 0 active tests update
18:37:58.322 config.js:550 Updating active tests display with 0 tests
18:37:58.322 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:37:59.429 config.js:966 viewTestDetails called for TSN: 18370
18:37:59.429 test-details-modal.js:62 Using JWT credentials from unified auth client: <EMAIL>
18:37:59.429 test-details-modal.js:206 Attempting to load test details from external API first...
18:37:59.430 reports.details-external.js:3 Loading test details from external API for 18370...
18:37:59.430 reports.details-external.js:7 🔧 External API Service found: true
18:37:59.430 reports.details-external.js:8 🔧 Service type: externalApiService
18:37:59.430 external-api-service.js:65 [ExternalApiService] 🔍 Session invalid: jsessionId=false, jsessionExpiry=false
18:37:59.430 reports.details-external.js:16 🔐 Session valid: false
18:37:59.430 reports.details-external.js:20 🔑 Logging in to external API...
18:37:59.430 reports.details-external.js:56 ❌ Login failed: TypeError: Cannot read properties of undefined (reading 'get')
    at loadTestDetailsFromExternalApi (reports.details-external.js:24:61)
    at TestDetailsModal.show (test-details-modal.js:209:41)
    at viewTestDetails (config.js:982:33)
    at HTMLButtonElement.<anonymous> (config.js:942:13)
loadTestDetailsFromExternalApi @ reports.details-external.js:56
show @ test-details-modal.js:209
viewTestDetails @ config.js:982
(anonymous) @ config.js:942
18:37:59.430 reports.details-external.js:362 ❌ EXTERNAL API ONLY MODE - Error in loadTestDetailsFromExternalApi for 18370: External API login failed: Cannot read properties of undefined (reading 'get') Error: External API login failed: Cannot read properties of undefined (reading 'get')
    at loadTestDetailsFromExternalApi (reports.details-external.js:57:23)
    at TestDetailsModal.show (test-details-modal.js:209:41)
    at viewTestDetails (config.js:982:33)
    at HTMLButtonElement.<anonymous> (config.js:942:13)
loadTestDetailsFromExternalApi @ reports.details-external.js:362
show @ test-details-modal.js:209
viewTestDetails @ config.js:982
(anonymous) @ config.js:942
18:37:59.431 reports.details-external.js:363 🚫 NO DATABASE FALLBACK - External API must be fixed to load test details
loadTestDetailsFromExternalApi @ reports.details-external.js:363
show @ test-details-modal.js:209
viewTestDetails @ config.js:982
(anonymous) @ config.js:942
18:37:59.431 test-details-modal.js:213 External API failed, trying other APIs: EXTERNAL API ONLY: Failed to load details for 18370 from external API: External API login failed: Cannot read properties of undefined (reading 'get'). Database fallback is disabled.
18:37:59.431 test-details-modal.js:238 ConfigApiService not available, trying database API...
18:37:59.431 reports.details-db.js:3 Loading test details from database API for test 18370...
18:37:59.431 reports.details-db.js:9 Using ApiService to load test details
18:37:59.431 unified-api-service.js:899 Getting test details for 18370 using endpoint /local/test-details
18:37:59.431 unified-api-service.js:314 Constructing URL: Base=http://localhost:3000, Endpoint=/local/test-details/18370, Full URL=http://localhost:3000/local/test-details/18370
18:37:59.431 unified-api-service.js:319 Making GET request to: http://localhost:3000/local/test-details/18370
18:37:59.431 config.js:1075 View Details clicked for TSN ID: 18370
18:37:59.432 test-details-modal.js:62 Using JWT credentials from unified auth client: <EMAIL>
18:37:59.432 test-details-modal.js:206 Attempting to load test details from external API first...
18:37:59.432 reports.details-external.js:3 Loading test details from external API for 18370...
18:37:59.432 reports.details-external.js:7 🔧 External API Service found: true
18:37:59.432 reports.details-external.js:8 🔧 Service type: externalApiService
18:37:59.432 external-api-service.js:65 [ExternalApiService] 🔍 Session invalid: jsessionId=false, jsessionExpiry=false
18:37:59.432 reports.details-external.js:16 🔐 Session valid: false
18:37:59.432 reports.details-external.js:20 🔑 Logging in to external API...
18:37:59.432 reports.details-external.js:56 ❌ Login failed: TypeError: Cannot read properties of undefined (reading 'get')
    at loadTestDetailsFromExternalApi (reports.details-external.js:24:61)
    at TestDetailsModal.show (test-details-modal.js:209:41)
    at HTMLButtonElement.handleViewDetailsClick (config.js:1084:33)
loadTestDetailsFromExternalApi @ reports.details-external.js:56
show @ test-details-modal.js:209
handleViewDetailsClick @ config.js:1084
18:37:59.432 reports.details-external.js:362 ❌ EXTERNAL API ONLY MODE - Error in loadTestDetailsFromExternalApi for 18370: External API login failed: Cannot read properties of undefined (reading 'get') Error: External API login failed: Cannot read properties of undefined (reading 'get')
    at loadTestDetailsFromExternalApi (reports.details-external.js:57:23)
    at TestDetailsModal.show (test-details-modal.js:209:41)
    at HTMLButtonElement.handleViewDetailsClick (config.js:1084:33)
loadTestDetailsFromExternalApi @ reports.details-external.js:362
show @ test-details-modal.js:209
handleViewDetailsClick @ config.js:1084
18:37:59.432 reports.details-external.js:363 🚫 NO DATABASE FALLBACK - External API must be fixed to load test details
loadTestDetailsFromExternalApi @ reports.details-external.js:363
show @ test-details-modal.js:209
handleViewDetailsClick @ config.js:1084
18:37:59.432 test-details-modal.js:213 External API failed, trying other APIs: EXTERNAL API ONLY: Failed to load details for 18370 from external API: External API login failed: Cannot read properties of undefined (reading 'get'). Database fallback is disabled.
18:37:59.432 test-details-modal.js:238 ConfigApiService not available, trying database API...
18:37:59.432 reports.details-db.js:3 Loading test details from database API for test 18370...
18:37:59.432 reports.details-db.js:9 Using ApiService to load test details
18:37:59.432 simple-optimizations.js:32 Request deduplication: testDetails_18370
18:38:00.318 unified-api-service.js:314 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
18:38:00.318 unified-api-service.js:319 Making GET request to: http://localhost:3000/local/recent-runs
18:38:00.318 unified-api-service.js:314 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
18:38:00.318 unified-api-service.js:319 Making GET request to: http://localhost:3000/local/recent-runs
18:38:02.313 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:38:04.314 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:38:06.314 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:38:08.311 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":50,"type":"single_case"}
18:38:08.311 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:38:10.319 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:38:10.855 unified-api-service.js:902 [DEBUG] getTestDetails: Raw API response: {success: true, test: {…}, data: {…}, message: 'Test details retrieved successfully'}
18:38:10.856 unified-api-service.js:907 [DEBUG] getTestDetails: Found test in response.test
18:38:10.856 unified-api-service.js:922 [DEBUG] getTestDetails: Test cases in result: 3
18:38:10.856 unified-api-service.js:924 [DEBUG] getTestDetails: First test case: {tc_id: '3180', test_case_name: 'Check pending feeds', description: 'check NO pending feeds\r\\n\r\\nbk copy:  3228', outcome: 'P', status: 'Passed', …}
18:38:10.856 simple-optimizations.js:47 Request completed: testDetails_18370
18:38:10.856 reports.details-db.js:26 currentState not available, skipping state storage
loadTestDetailsFromDatabaseApi @ reports.details-db.js:26
await in loadTestDetailsFromDatabaseApi
show @ test-details-modal.js:241
await in show
viewTestDetails @ config.js:982
(anonymous) @ config.js:942
18:38:10.856 reports.details-db.js:29 Test details loaded from database API via ApiService: {tsn_id: '18370', test_id: '3180', test_name: 'Check pending feeds', type: 'Test Case', envir: 'qa02', …}
18:38:10.856 reports.details-db.js:26 currentState not available, skipping state storage
loadTestDetailsFromDatabaseApi @ reports.details-db.js:26
await in loadTestDetailsFromDatabaseApi
show @ test-details-modal.js:241
await in show
handleViewDetailsClick @ config.js:1084
18:38:10.856 reports.details-db.js:29 Test details loaded from database API via ApiService: {tsn_id: '18370', test_id: '3180', test_name: 'Check pending feeds', type: 'Test Case', envir: 'qa02', …}
18:38:10.856 test-details-modal.js:242 Successfully loaded from database API: {tsn_id: '18370', test_id: '3180', test_name: 'Check pending feeds', type: 'Test Case', envir: 'qa02', …}
18:38:10.857 test-details-modal.js:242 Successfully loaded from database API: {tsn_id: '18370', test_id: '3180', test_name: 'Check pending feeds', type: 'Test Case', envir: 'qa02', …}
18:38:12.320 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:38:14.313 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:38:15.496 simple-optimizations.js:47 Request completed: recentRuns_{"limit":50,"type":"single_case"}
18:38:15.496 config.js:488 Received 50 recent runs update
18:38:15.496 config.js:597 processRecentRunsData called with 50 runs
18:38:15.496 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:15.496 config.js:880 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
18:38:15.497 config.js:881 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
18:38:15.497 config.js:899 Rendering 50 recent runs
18:38:15.499 config.js:488 Received 50 recent runs update
18:38:15.499 config.js:597 processRecentRunsData called with 50 runs
18:38:15.499 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:15.499 config.js:880 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
18:38:15.499 config.js:881 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
18:38:15.500 config.js:899 Rendering 50 recent runs
18:38:16.313 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":50,"type":"single_case"}
18:38:16.314 config.js:488 Received 50 recent runs update
18:38:16.314 config.js:597 processRecentRunsData called with 50 runs
18:38:16.314 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:16.315 config.js:880 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
18:38:16.317 config.js:881 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
18:38:16.319 config.js:899 Rendering 50 recent runs
18:38:16.340 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:38:18.063 simple-optimizations.js:47 Request completed: recentRuns_{"limit":100,"type":"single_case"}
18:38:18.063 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:38:18.063 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:38:18.063 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:38:18.063 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:38:18.063 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:38:18.063 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:38:18.063 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:38:18.063 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:38:18.063 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:38:18.063 config.js:496 Received 0 active tests update
18:38:18.063 config.js:550 Updating active tests display with 0 tests
18:38:18.064 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:18.064 config.js:496 Received 0 active tests update
18:38:18.064 config.js:550 Updating active tests display with 0 tests
18:38:18.064 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:18.064 config.js:496 Received 0 active tests update
18:38:18.064 config.js:550 Updating active tests display with 0 tests
18:38:18.064 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:18.065 config.js:496 Received 0 active tests update
18:38:18.065 config.js:550 Updating active tests display with 0 tests
18:38:18.065 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:18.065 config.js:496 Received 0 active tests update
18:38:18.065 config.js:550 Updating active tests display with 0 tests
18:38:18.065 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:18.065 config.js:496 Received 0 active tests update
18:38:18.066 config.js:550 Updating active tests display with 0 tests
18:38:18.066 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:18.066 config.js:496 Received 0 active tests update
18:38:18.066 config.js:550 Updating active tests display with 0 tests
18:38:18.066 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:18.066 config.js:496 Received 0 active tests update
18:38:18.066 config.js:550 Updating active tests display with 0 tests
18:38:18.067 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:18.067 config.js:496 Received 0 active tests update
18:38:18.067 config.js:550 Updating active tests display with 0 tests
18:38:18.067 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:18.320 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
18:38:18.321 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:38:18.321 config.js:496 Received 0 active tests update
18:38:18.321 config.js:550 Updating active tests display with 0 tests
18:38:18.321 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:20.323 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
18:38:20.323 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:38:20.323 config.js:496 Received 0 active tests update
18:38:20.324 config.js:550 Updating active tests display with 0 tests
18:38:20.324 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:22.316 unified-api-service.js:314 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
18:38:22.317 unified-api-service.js:319 Making GET request to: http://localhost:3000/local/recent-runs
18:38:24.318 unified-api-service.js:314 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
18:38:24.318 unified-api-service.js:319 Making GET request to: http://localhost:3000/local/recent-runs
18:38:24.319 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:38:26.325 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:38:28.315 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:38:30.316 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:38:32.317 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":50,"type":"single_case"}
18:38:32.317 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:38:34.311 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:38:36.325 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:38:38.322 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:38:39.956 simple-optimizations.js:47 Request completed: recentRuns_{"limit":50,"type":"single_case"}
18:38:39.956 config.js:488 Received 50 recent runs update
18:38:39.957 config.js:597 processRecentRunsData called with 50 runs
18:38:39.957 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:39.957 config.js:880 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
18:38:39.957 config.js:881 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
18:38:39.958 config.js:899 Rendering 50 recent runs
18:38:39.959 config.js:488 Received 50 recent runs update
18:38:39.959 config.js:597 processRecentRunsData called with 50 runs
18:38:39.959 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:39.960 config.js:880 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
18:38:39.960 config.js:881 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
18:38:39.960 config.js:899 Rendering 50 recent runs
18:38:40.326 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":50,"type":"single_case"}
18:38:40.326 config.js:488 Received 50 recent runs update
18:38:40.326 config.js:597 processRecentRunsData called with 50 runs
18:38:40.326 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:40.326 config.js:880 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
18:38:40.326 config.js:881 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
18:38:40.327 config.js:899 Rendering 50 recent runs
18:38:40.342 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:38:40.453 simple-optimizations.js:47 Request completed: recentRuns_{"limit":100,"type":"single_case"}
18:38:40.453 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:38:40.453 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:38:40.453 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:38:40.454 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:38:40.454 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:38:40.454 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:38:40.454 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:38:40.454 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:38:40.454 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:38:40.454 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:38:40.454 config.js:496 Received 0 active tests update
18:38:40.454 config.js:550 Updating active tests display with 0 tests
18:38:40.454 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:40.454 config.js:496 Received 0 active tests update
18:38:40.454 config.js:550 Updating active tests display with 0 tests
18:38:40.454 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:40.454 config.js:496 Received 0 active tests update
18:38:40.454 config.js:550 Updating active tests display with 0 tests
18:38:40.454 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:40.454 config.js:496 Received 0 active tests update
18:38:40.454 config.js:550 Updating active tests display with 0 tests
18:38:40.454 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:40.454 config.js:496 Received 0 active tests update
18:38:40.454 config.js:550 Updating active tests display with 0 tests
18:38:40.454 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:40.454 config.js:496 Received 0 active tests update
18:38:40.454 config.js:550 Updating active tests display with 0 tests
18:38:40.454 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:40.454 config.js:496 Received 0 active tests update
18:38:40.454 config.js:550 Updating active tests display with 0 tests
18:38:40.454 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:40.454 config.js:496 Received 0 active tests update
18:38:40.454 config.js:550 Updating active tests display with 0 tests
18:38:40.454 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:40.454 config.js:496 Received 0 active tests update
18:38:40.454 config.js:550 Updating active tests display with 0 tests
18:38:40.454 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:40.454 config.js:496 Received 0 active tests update
18:38:40.454 config.js:550 Updating active tests display with 0 tests
18:38:40.454 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:42.316 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
18:38:42.316 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:38:42.316 config.js:496 Received 0 active tests update
18:38:42.317 config.js:550 Updating active tests display with 0 tests
18:38:42.317 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:38:44.323 unified-api-service.js:314 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
18:38:44.323 unified-api-service.js:319 Making GET request to: http://localhost:3000/local/recent-runs
18:38:46.324 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:38:48.312 unified-api-service.js:314 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
18:38:48.312 unified-api-service.js:319 Making GET request to: http://localhost:3000/local/recent-runs
18:38:48.313 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:38:50.313 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:38:52.315 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:38:54.320 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:38:56.316 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":50,"type":"single_case"}
18:38:56.316 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:38:58.316 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:39:00.320 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:39:00.481 simple-optimizations.js:47 Request completed: recentRuns_{"limit":100,"type":"single_case"}
18:39:00.481 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:39:00.481 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:39:00.481 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:39:00.481 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:39:00.481 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:39:00.481 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:39:00.481 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:39:00.481 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:39:00.481 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:39:00.481 config.js:496 Received 0 active tests update
18:39:00.482 config.js:550 Updating active tests display with 0 tests
18:39:00.482 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:00.482 config.js:496 Received 0 active tests update
18:39:00.482 config.js:550 Updating active tests display with 0 tests
18:39:00.482 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:00.482 config.js:496 Received 0 active tests update
18:39:00.482 config.js:550 Updating active tests display with 0 tests
18:39:00.482 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:00.482 config.js:496 Received 0 active tests update
18:39:00.482 config.js:550 Updating active tests display with 0 tests
18:39:00.482 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:00.482 config.js:496 Received 0 active tests update
18:39:00.482 config.js:550 Updating active tests display with 0 tests
18:39:00.482 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:00.482 config.js:496 Received 0 active tests update
18:39:00.482 config.js:550 Updating active tests display with 0 tests
18:39:00.482 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:00.482 config.js:496 Received 0 active tests update
18:39:00.482 config.js:550 Updating active tests display with 0 tests
18:39:00.482 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:00.482 config.js:496 Received 0 active tests update
18:39:00.482 config.js:550 Updating active tests display with 0 tests
18:39:00.482 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:00.482 config.js:496 Received 0 active tests update
18:39:00.482 config.js:550 Updating active tests display with 0 tests
18:39:00.482 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:02.311 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
18:39:02.311 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:39:02.311 config.js:496 Received 0 active tests update
18:39:02.311 config.js:550 Updating active tests display with 0 tests
18:39:02.311 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:02.454 simple-optimizations.js:47 Request completed: recentRuns_{"limit":50,"type":"single_case"}
18:39:02.454 config.js:488 Received 50 recent runs update
18:39:02.454 config.js:597 processRecentRunsData called with 50 runs
18:39:02.454 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:02.454 config.js:880 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
18:39:02.455 config.js:881 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
18:39:02.455 config.js:899 Rendering 50 recent runs
18:39:02.457 config.js:488 Received 50 recent runs update
18:39:02.457 config.js:597 processRecentRunsData called with 50 runs
18:39:02.457 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:02.457 config.js:880 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
18:39:02.457 config.js:881 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
18:39:02.457 config.js:899 Rendering 50 recent runs
18:39:04.325 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":50,"type":"single_case"}
18:39:04.326 config.js:488 Received 50 recent runs update
18:39:04.326 config.js:597 processRecentRunsData called with 50 runs
18:39:04.326 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:04.326 config.js:880 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
18:39:04.327 config.js:881 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
18:39:04.330 config.js:899 Rendering 50 recent runs
18:39:04.370 unified-api-service.js:314 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
18:39:04.370 unified-api-service.js:319 Making GET request to: http://localhost:3000/local/recent-runs
18:39:06.321 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:39:08.311 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:39:10.317 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:39:12.324 unified-api-service.js:314 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
18:39:12.324 unified-api-service.js:319 Making GET request to: http://localhost:3000/local/recent-runs
18:39:12.326 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:39:14.325 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:39:16.314 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:39:18.316 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:39:20.323 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":50,"type":"single_case"}
18:39:20.323 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:39:20.674 simple-optimizations.js:47 Request completed: recentRuns_{"limit":100,"type":"single_case"}
18:39:20.674 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:39:20.674 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:39:20.674 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:39:20.674 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:39:20.674 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:39:20.674 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:39:20.674 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:39:20.674 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:39:20.675 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:39:20.675 config.js:496 Received 0 active tests update
18:39:20.675 config.js:550 Updating active tests display with 0 tests
18:39:20.675 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:20.675 config.js:496 Received 0 active tests update
18:39:20.675 config.js:550 Updating active tests display with 0 tests
18:39:20.675 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:20.676 config.js:496 Received 0 active tests update
18:39:20.676 config.js:550 Updating active tests display with 0 tests
18:39:20.676 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:20.676 config.js:496 Received 0 active tests update
18:39:20.676 config.js:550 Updating active tests display with 0 tests
18:39:20.676 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:20.677 config.js:496 Received 0 active tests update
18:39:20.677 config.js:550 Updating active tests display with 0 tests
18:39:20.677 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:20.677 config.js:496 Received 0 active tests update
18:39:20.677 config.js:550 Updating active tests display with 0 tests
18:39:20.677 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:20.677 config.js:496 Received 0 active tests update
18:39:20.677 config.js:550 Updating active tests display with 0 tests
18:39:20.677 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:20.678 config.js:496 Received 0 active tests update
18:39:20.678 config.js:550 Updating active tests display with 0 tests
18:39:20.678 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:20.678 config.js:496 Received 0 active tests update
18:39:20.678 config.js:550 Updating active tests display with 0 tests
18:39:20.678 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:22.318 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100,"type":"single_case"}
18:39:22.319 simple-optimizations.js:281 Found 0 active tests from 100 recent runs
18:39:22.319 config.js:496 Received 0 active tests update
18:39:22.319 config.js:550 Updating active tests display with 0 tests
18:39:22.319 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:24.322 unified-api-service.js:314 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
18:39:24.322 unified-api-service.js:319 Making GET request to: http://localhost:3000/local/recent-runs
18:39:25.676 simple-optimizations.js:47 Request completed: recentRuns_{"limit":50,"type":"single_case"}
18:39:25.677 config.js:488 Received 50 recent runs update
18:39:25.677 config.js:597 processRecentRunsData called with 50 runs
18:39:25.677 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:25.677 config.js:880 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
18:39:25.678 config.js:881 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
18:39:25.680 config.js:899 Rendering 50 recent runs
18:39:25.683 config.js:488 Received 50 recent runs update
18:39:25.683 config.js:597 processRecentRunsData called with 50 runs
18:39:25.683 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:25.683 config.js:880 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
18:39:25.684 config.js:881 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
18:39:25.684 config.js:899 Rendering 50 recent runs
18:39:26.323 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:39:28.323 simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":50,"type":"single_case"}
18:39:28.323 config.js:488 Received 50 recent runs update
18:39:28.323 config.js:597 processRecentRunsData called with 50 runs
18:39:28.323 config.js:695 🔄 renderActiveTests called, active tests count: 0
18:39:28.324 config.js:880 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
18:39:28.325 config.js:881 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
18:39:28.328 config.js:899 Rendering 50 recent runs
18:39:28.349 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:39:30.313 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}
18:39:32.318 simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100,"type":"single_case"}